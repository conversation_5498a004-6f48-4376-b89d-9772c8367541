#!/usr/bin/env python3
"""
Test Enhanced EMA System
========================

This script tests the enhanced EMA calculation system with:
1. Improved EMA calculations (pandas-ta support)
2. Enhanced CSV management (update vs recreate)
3. Better historical data management (2 days of 1min data)
4. Robust crossover detection

Usage:
    python test_enhanced_ema_system.py [--recreate-csv]
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
import logging

# Add src directory to path
sys.path.append('src')

from core.ema import EMACalculator
from data.logger import SignalLogger
from data.enhanced_historical_manager import EnhancedHistoricalManager


def setup_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'test_enhanced_ema_{datetime.now().strftime("%Y%m%d")}.log')
        ]
    )


def test_enhanced_ema_calculator():
    """Test the enhanced EMA calculator"""
    print("\n🧮 Testing Enhanced EMA Calculator")
    print("-" * 50)
    
    # Configuration
    ema_combinations = [
        {"short_ema": 5, "long_ema": 10},
        {"short_ema": 8, "long_ema": 21}
    ]
    
    # Create enhanced EMA calculator
    calculator = EMACalculator(ema_combinations, max_history=2880)  # 2 days of 1min data
    
    # Generate test price data (simulating 2 days of 1min data)
    print("Generating test price data...")
    base_price = 24500.0
    test_prices = []
    timestamps = []
    
    # Generate 200 data points for testing
    for i in range(200):
        # Add some realistic price movement
        price_change = (i % 10 - 5) * 2.5  # Oscillating movement
        trend = i * 0.1  # Slight upward trend
        noise = (i % 3 - 1) * 1.0  # Random noise
        
        price = base_price + price_change + trend + noise
        test_prices.append(price)
        
        timestamp = datetime.now() - timedelta(minutes=200-i)
        timestamps.append(timestamp)
    
    print(f"Generated {len(test_prices)} test prices")
    print(f"Price range: {min(test_prices):.2f} - {max(test_prices):.2f}")
    
    # Load historical data
    print("\nLoading historical data into EMA calculator...")
    calculator.load_state_from_prices("1min", test_prices, timestamps)
    
    # Get current EMA values
    current_emas = calculator.get_current_ema_values("1min")
    print(f"Current EMAs: {current_emas}")
    
    # Test real-time updates
    print("\nTesting real-time EMA updates...")
    for i in range(5):
        new_price = test_prices[-1] + (i - 2) * 5.0  # Some price movement
        new_timestamp = timestamps[-1] + timedelta(minutes=i+1)
        
        emas = calculator.add_price("1min", new_price, new_timestamp)
        print(f"Price {new_price:.2f}: EMAs = {emas}")
        
        # Check for crossover signals
        signals = calculator.get_crossover_signals("1min")
        if signals:
            for signal in signals:
                print(f"  🔔 Signal: {signal['signal']} - {signal['short_ema']}/{signal['long_ema']}")
    
    return True


def test_enhanced_csv_management(recreate_csv=False):
    """Test enhanced CSV management"""
    print("\n📄 Testing Enhanced CSV Management")
    print("-" * 50)
    
    # Create test data directory
    os.makedirs("test_data", exist_ok=True)
    
    # Create signal logger
    signal_logger = SignalLogger("test_data", initial_capital=100000)
    
    if recreate_csv:
        print("Recreating daily CSV file...")
        signal_logger.recreate_daily_csv()
    
    # Test signal logging
    test_signals = [
        {
            'datetime': datetime.now(),
            'action': 'BUY',
            'price': 24750.50,
            'ohlc': {
                'open': 24745.00,
                'high': 24755.00,
                'low': 24740.00,
                'close': 24750.50,
                'volume': 1500
            },
            'short_ema_value': 24748.25,
            'long_ema_value': 24745.80,
            'pnl': 0.0
        },
        {
            'datetime': datetime.now() + timedelta(minutes=5),
            'action': 'SELL',
            'price': 24735.75,
            'ohlc': {
                'open': 24750.00,
                'high': 24752.00,
                'low': 24735.00,
                'close': 24735.75,
                'volume': 1200
            },
            'short_ema_value': 24740.60,
            'long_ema_value': 24743.45,
            'pnl': -14.75
        }
    ]
    
    print(f"Logging {len(test_signals)} test signals...")
    for signal in test_signals:
        signal_logger.log_signal(signal)
    
    # Get statistics
    stats = signal_logger.get_statistics()
    print(f"Logger statistics: {stats}")
    
    # Test CSV file existence and content
    csv_file = stats.get('csv_file')
    if csv_file and os.path.exists(csv_file):
        with open(csv_file, 'r') as f:
            lines = f.readlines()
        print(f"CSV file: {csv_file}")
        print(f"Total lines: {len(lines)} (including header)")
        
        if len(lines) > 1:
            print("Recent signals:")
            for line in lines[-3:]:
                if line.strip() and not line.startswith('Date'):
                    fields = line.strip().split(',')
                    if len(fields) >= 4:
                        print(f"  {fields[1]} - {fields[2]} @ {fields[3]}")
    
    signal_logger.close()
    return True


def test_enhanced_historical_manager():
    """Test enhanced historical data manager"""
    print("\n📊 Testing Enhanced Historical Data Manager")
    print("-" * 50)
    
    # Create enhanced historical manager
    hist_manager = EnhancedHistoricalManager("test_data")
    
    # Add some test data
    print("Adding test historical data...")
    base_time = datetime.now() - timedelta(hours=2)
    
    for i in range(120):  # 2 hours of 1min data
        timestamp = base_time + timedelta(minutes=i)
        price = 24500.0 + (i % 20 - 10) * 2.0  # Oscillating prices
        
        ohlc_data = {
            'open': price - 1.0,
            'high': price + 2.0,
            'low': price - 2.0,
            'close': price,
            'volume': 1000 + (i % 100)
        }
        
        hist_manager.add_price_data(timestamp, price, ohlc_data)
    
    # Get historical data
    historical_prices = hist_manager.get_historical_prices(days=1)
    print(f"Retrieved {len(historical_prices)} historical prices")
    
    # Get data with timestamps
    prices, timestamps = hist_manager.get_historical_data_with_timestamps(days=1)
    print(f"Retrieved {len(prices)} prices with timestamps")
    
    # Get summary
    summary = hist_manager.get_data_summary()
    print(f"Data summary: {summary}")
    
    # Force save and cleanup
    hist_manager.force_save()
    hist_manager.cleanup_old_data()
    
    return True


def main():
    """Main test function"""
    parser = argparse.ArgumentParser(description='Test Enhanced EMA System')
    parser.add_argument('--recreate-csv', action='store_true',
                       help='Recreate CSV file instead of updating existing')
    args = parser.parse_args()
    
    print("=" * 60)
    print("TESTING ENHANCED EMA SYSTEM")
    print("=" * 60)
    
    setup_logging()
    
    try:
        # Test 1: Enhanced EMA Calculator
        success1 = test_enhanced_ema_calculator()
        
        # Test 2: Enhanced CSV Management
        success2 = test_enhanced_csv_management(recreate_csv=args.recreate_csv)
        
        # Test 3: Enhanced Historical Data Manager
        success3 = test_enhanced_historical_manager()
        
        print("\n" + "=" * 60)
        if success1 and success2 and success3:
            print("🎉 ALL ENHANCED TESTS PASSED!")
            print("\nKey Improvements Tested:")
            print("  ✅ Enhanced EMA calculations with pandas-ta support")
            print("  ✅ Improved CSV management (update vs recreate)")
            print("  ✅ Enhanced historical data management (2 days of 1min data)")
            print("  ✅ Robust crossover detection")
            print("  ✅ Better state persistence")
        else:
            print("⚠️  SOME TESTS FAILED")
            if not success1:
                print("- Enhanced EMA calculator test failed")
            if not success2:
                print("- Enhanced CSV management test failed")
            if not success3:
                print("- Enhanced historical data manager test failed")
        
        print("\nNext Steps:")
        print("1. Check test_data/ directory for generated files")
        print("2. Review log files for detailed information")
        print("3. Run with --recreate-csv to test CSV recreation")
        print("4. Integrate with live system for production testing")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
