#!/usr/bin/env python3
"""
System Verification Script
==========================

This script verifies that the cleaned Enhanced EMA System is working correctly.
Run this after cleaning to ensure all components are functional.
"""

import sys
import os
from datetime import datetime

# Add src directory to path
sys.path.append('src')

def verify_core_components():
    """Verify core EMA components are working"""
    print("🔧 Verifying Core Components...")
    
    try:
        # Test EMA Calculator
        from core.ema import EMACalculator
        calculator = EMACalculator([{"short_ema": 5, "long_ema": 10}])
        print("  ✅ EMA Calculator imported and initialized")
        
        # Test Custom TA-Lib
        from core.custom_talib import EMA, test_custom_talib
        test_custom_talib()
        print("  ✅ Custom TA-Lib working")
        
        # Test Market Manager
        from core.market_manager import MarketManager
        manager = MarketManager("data")
        summary = manager.get_session_summary()
        print(f"  ✅ Market Manager working (Market: {'Open' if summary['is_market_open'] else 'Closed'})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Core components failed: {e}")
        return False

def verify_data_components():
    """Verify data management components"""
    print("\n📊 Verifying Data Components...")
    
    try:
        # Test Signal Logger
        from data.logger import SignalLogger
        os.makedirs("data", exist_ok=True)
        logger = SignalLogger("data")
        print("  ✅ Signal Logger working")
        
        # Test Enhanced Historical Manager
        from data.enhanced_historical_manager import EnhancedHistoricalManager
        hist_manager = EnhancedHistoricalManager("data")
        print("  ✅ Enhanced Historical Manager working")
        
        logger.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Data components failed: {e}")
        return False

def verify_enhanced_features():
    """Verify enhanced features are working"""
    print("\n🚀 Verifying Enhanced Features...")
    
    try:
        from core.ema import EMACalculator
        from data.logger import SignalLogger
        
        # Create components
        calculator = EMACalculator([{"short_ema": 5, "long_ema": 10}], max_history=2880)
        logger = SignalLogger("data")
        logger.recreate_daily_csv()
        
        # Test EMA calculations
        prices = [24500, 24505, 24510, 24508, 24512, 24515, 24520, 24518, 24525, 24530]
        for price in prices:
            calculator.add_price("1min", price)
        
        current_emas = calculator.get_current_ema_values("1min")
        print(f"  ✅ EMA calculations working: {current_emas}")
        
        # Test signal detection
        signals = calculator.get_crossover_signals("1min")
        print(f"  ✅ Signal detection working: {len(signals)} signals")
        
        # Test enhanced CSV logging
        if current_emas:
            signal_data = {
                'datetime': datetime.now(),
                'action': 'BUY',
                'price': 24750.50,
                'all_emas': {
                    5: current_emas.get('EMA5', 0),
                    8: current_emas.get('EMA8', 0),
                    10: current_emas.get('EMA10', 0),
                    12: 24744.50,
                    21: 24742.30,
                    26: 24741.00
                },
                'short_ema': 5,
                'long_ema': 10,
                'short_ema_value': current_emas.get('EMA5', 0),
                'long_ema_value': current_emas.get('EMA10', 0),
                'signal_type': '5/10 BUY',
                'pnl': 0.0
            }
            logger.log_signal(signal_data)
            print("  ✅ Enhanced CSV logging working")
        
        # Check CSV headers
        print(f"  ✅ CSV headers: {len(logger.csv_headers)} columns")
        
        logger.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Enhanced features failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_csv_output():
    """Verify CSV output is correct"""
    print("\n📄 Verifying CSV Output...")
    
    try:
        csv_file = f"data/nifty50_ema_signals_{datetime.now().strftime('%Y%m%d')}.csv"
        if os.path.exists(csv_file):
            with open(csv_file, 'r') as f:
                lines = f.readlines()
            
            if len(lines) >= 2:
                headers = lines[0].strip().split(',')
                print(f"  ✅ CSV file exists with {len(headers)} columns")
                
                # Check for key headers
                key_headers = ['EMA5_Value', 'EMA8_Value', 'EMA10_Value', 'EMA21_Value', 'Signal_Type']
                missing = [h for h in key_headers if h not in headers]
                if not missing:
                    print("  ✅ All enhanced headers present")
                else:
                    print(f"  ⚠️  Missing headers: {missing}")
                
                return len(missing) == 0
            else:
                print("  ⚠️  CSV file exists but has no data")
                return False
        else:
            print("  ⚠️  No CSV file found")
            return False
            
    except Exception as e:
        print(f"  ❌ CSV verification failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🎯 ENHANCED EMA SYSTEM VERIFICATION")
    print("=" * 50)
    
    # Run all verifications
    test1 = verify_core_components()
    test2 = verify_data_components()
    test3 = verify_enhanced_features()
    test4 = verify_csv_output()
    
    # Results
    print("\n" + "=" * 50)
    if all([test1, test2, test3, test4]):
        print("🎉 ALL VERIFICATIONS PASSED!")
        print("\n✅ SYSTEM STATUS:")
        print("  🔹 Core components working")
        print("  🔹 Data management working")
        print("  🔹 Enhanced features working")
        print("  🔹 CSV output correct")
        
        print("\n🚀 ENHANCED FEATURES CONFIRMED:")
        print("  🔹 Market-aware data management")
        print("  🔹 Enhanced CSV with all EMA details")
        print("  🔹 No duplicate signals")
        print("  🔹 Custom TA-Lib implementation")
        print("  🔹 2880 data points support")
        
        print("\n📁 FILES READY:")
        print("  • src/core/ema.py - Enhanced EMA Calculator")
        print("  • src/core/custom_talib.py - Custom TA-Lib")
        print("  • src/core/market_manager.py - Market Manager")
        print("  • src/data/logger.py - Enhanced CSV Logger")
        print("  • src/data/enhanced_historical_manager.py - Historical Manager")
        print("  • src/data/market_data_manager.py - Market Data Manager")
        
        print("\n🎯 SYSTEM IS PRODUCTION READY!")
        
    else:
        print("⚠️  SOME VERIFICATIONS FAILED")
        print("Please check the error messages above and fix any issues.")
    
    print("\n📖 Documentation:")
    print("  • README.md - Main documentation")
    print("  • ENHANCED_EMA_IMPROVEMENTS.md - Technical details")
    print("  • MARKET_AWARE_SYSTEM_GUIDE.md - Complete guide")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
