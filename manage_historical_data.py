#!/usr/bin/env python3
"""
Historical Data Management
==========================

Management script for historical data operations in the Enhanced EMA Trading System.
Provides database status, updates, and EMA testing functionality.
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class HistoricalDataManager:
    def __init__(self, data_dir="data"):
        self.data_dir = data_dir
        self.historical_dir = os.path.join(data_dir, "historical")
        self.enhanced_dir = os.path.join(data_dir, "historical_enhanced")
        
        # Ensure directories exist
        os.makedirs(self.historical_dir, exist_ok=True)
        os.makedirs(self.enhanced_dir, exist_ok=True)
    
    def status(self):
        """Check historical data status"""
        print("📊 HISTORICAL DATA STATUS")
        print("=" * 40)
        
        # Check historical data files
        historical_csv = os.path.join(self.historical_dir, "nifty50_historical.csv")
        historical_pkl = os.path.join(self.historical_dir, "nifty50_historical.pkl")
        metadata_file = os.path.join(self.historical_dir, "metadata.json")
        
        print("📁 Data Files:")
        for file_path, name in [
            (historical_csv, "Historical CSV"),
            (historical_pkl, "Historical PKL"),
            (metadata_file, "Metadata")
        ]:
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                size_mb = stat.st_size / 1024 / 1024
                modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f"  ✅ {name}: {size_mb:.2f} MB (modified: {modified})")
            else:
                print(f"  ❌ {name}: Not found")
        
        # Check data completeness
        try:
            from data.enhanced_historical_manager import EnhancedHistoricalManager
            manager = EnhancedHistoricalManager(self.data_dir)
            
            # Get data info
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3)
            
            data = manager.get_historical_data("NIFTY50", start_date, end_date, "1min")
            
            if data:
                print(f"\n📈 Data Summary:")
                print(f"  📊 Records: {len(data)}")
                print(f"  📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
                print(f"  ⏰ Expected records (3 days): ~1125")
                
                completeness = (len(data) / 1125) * 100
                print(f"  📋 Completeness: {completeness:.1f}%")
                
                if completeness >= 80:
                    print("  ✅ Data quality: Good")
                elif completeness >= 50:
                    print("  ⚠️  Data quality: Partial")
                else:
                    print("  ❌ Data quality: Poor")
            else:
                print("\n❌ No historical data available")
                
        except Exception as e:
            print(f"\n❌ Error checking data: {e}")
    
    def update(self):
        """Update historical data"""
        print("🔄 UPDATING HISTORICAL DATA")
        print("=" * 40)
        
        try:
            from data.enhanced_historical_manager import EnhancedHistoricalManager
            
            manager = EnhancedHistoricalManager(self.data_dir)
            
            # Update data for last 3 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3)
            
            print(f"📅 Updating data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            # This would typically fetch from DhanHQ or other data source
            # For now, we'll generate sample data
            print("📊 Generating sample historical data...")
            
            sample_data = []
            base_price = 24800
            
            for i in range(1125):  # 3 days of 1-minute data
                timestamp = start_date + timedelta(minutes=i)
                price_variation = (i % 20 - 10) * 2
                price = base_price + price_variation + (i * 0.01)
                
                sample_data.append({
                    'timestamp': timestamp,
                    'open': price,
                    'high': price + 5,
                    'low': price - 5,
                    'close': price,
                    'volume': 1000 + (i % 500)
                })
            
            # Save sample data
            manager.save_historical_data("NIFTY50", sample_data)
            
            print(f"✅ Updated {len(sample_data)} records")
            print("📊 Historical data update completed")
            
        except Exception as e:
            print(f"❌ Error updating data: {e}")
    
    def info(self):
        """Show detailed information"""
        print("📋 DETAILED HISTORICAL DATA INFORMATION")
        print("=" * 50)
        
        # Show directory structure
        print("📁 Directory Structure:")
        for root, dirs, files in os.walk(self.data_dir):
            level = root.replace(self.data_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                size = os.path.getsize(file_path)
                print(f"{subindent}{file} ({size} bytes)")
        
        # Show configuration
        print(f"\n⚙️  Configuration:")
        print(f"  📁 Data directory: {self.data_dir}")
        print(f"  📁 Historical directory: {self.historical_dir}")
        print(f"  📁 Enhanced directory: {self.enhanced_dir}")
        
        # Show system info
        print(f"\n🖥️  System Information:")
        print(f"  🐍 Python version: {sys.version}")
        print(f"  📂 Working directory: {os.getcwd()}")
        print(f"  ⏰ Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def test_ema(self):
        """Test EMA initialization with historical data"""
        print("🧪 TESTING EMA INITIALIZATION")
        print("=" * 40)
        
        try:
            from core.ema import EMACalculator
            from data.enhanced_historical_manager import EnhancedHistoricalManager
            
            # Get historical data
            manager = EnhancedHistoricalManager(self.data_dir)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3)
            
            data = manager.get_historical_data("NIFTY50", start_date, end_date, "1min")
            
            if not data:
                print("❌ No historical data available for testing")
                return
            
            print(f"📊 Testing with {len(data)} historical records")
            
            # Create EMA calculator
            calculator = EMACalculator([{"short_ema": 5, "long_ema": 10}])
            
            # Extract prices and timestamps
            prices = [record['close'] for record in data]
            timestamps = [record['timestamp'] for record in data]
            
            print("🔧 Loading historical data into EMA calculator...")
            calculator.load_state_from_prices("1min", prices, timestamps)
            
            # Check EMA values
            current_emas = calculator.get_current_ema_values("1min")
            
            if current_emas:
                print("✅ EMA initialization successful!")
                print(f"📈 Current EMA values: {current_emas}")
                
                # Check if EMAs are reasonable
                last_price = prices[-1]
                ema5_val = current_emas.get('EMA5', 0)
                ema10_val = current_emas.get('EMA10', 0)
                
                gap5 = abs(ema5_val - last_price)
                gap10 = abs(ema10_val - last_price)
                
                print(f"📊 Last price: {last_price:.2f}")
                print(f"📊 EMA5 gap: {gap5:.2f} points")
                print(f"📊 EMA10 gap: {gap10:.2f} points")
                
                if gap5 < 100 and gap10 < 100:
                    print("✅ EMA values are reasonable (within 100 points of price)")
                else:
                    print("⚠️  EMA values may need adjustment")
                
                # Test crossover detection
                print("\n🔍 Testing crossover detection...")
                test_prices = [last_price + 10, last_price + 20, last_price + 15, last_price + 5]
                
                signals_detected = 0
                for price in test_prices:
                    emas = calculator.add_price("1min", price, datetime.now())
                    signals = calculator.get_crossover_signals("1min")
                    if signals:
                        signals_detected += 1
                        print(f"🔔 Signal detected: {signals[0]['signal']}")
                
                print(f"📊 Crossover test: {signals_detected} signals detected")
                print("✅ EMA system is ready for production!")
                
            else:
                print("❌ EMA initialization failed")
                
        except Exception as e:
            print(f"❌ Error testing EMA: {e}")
            import traceback
            traceback.print_exc()
    
    def reset(self):
        """Reset/clean historical database"""
        print("🗑️  RESETTING HISTORICAL DATA")
        print("=" * 40)
        
        import shutil
        
        try:
            # Backup existing data
            backup_dir = f"data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if os.path.exists(self.data_dir):
                print(f"📦 Creating backup: {backup_dir}")
                shutil.copytree(self.data_dir, backup_dir)
            
            # Clean directories
            for dir_path in [self.historical_dir, self.enhanced_dir]:
                if os.path.exists(dir_path):
                    print(f"🗑️  Cleaning: {dir_path}")
                    shutil.rmtree(dir_path)
                    os.makedirs(dir_path, exist_ok=True)
            
            print("✅ Historical data reset completed")
            print(f"📦 Backup available at: {backup_dir}")
            
        except Exception as e:
            print(f"❌ Error resetting data: {e}")

def main():
    """Main management function"""
    parser = argparse.ArgumentParser(description="Historical Data Management")
    parser.add_argument("command", 
                       choices=["status", "update", "info", "test-ema", "reset"],
                       help="Management command")
    parser.add_argument("--data-dir", default="data", 
                       help="Data directory path")
    
    args = parser.parse_args()
    
    manager = HistoricalDataManager(args.data_dir)
    
    if args.command == "status":
        manager.status()
    elif args.command == "update":
        manager.update()
    elif args.command == "info":
        manager.info()
    elif args.command == "test-ema":
        manager.test_ema()
    elif args.command == "reset":
        manager.reset()

if __name__ == "__main__":
    main()
