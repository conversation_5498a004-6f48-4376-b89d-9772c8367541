#!/usr/bin/env python3
"""
Historical Data Management
==========================

Command-line tool for managing the 2-week historical database.
Provides utilities for updating, viewing, and maintaining historical data.

Usage:
    python manage_historical_data.py status      # Show database status
    python manage_historical_data.py update      # Update historical data
    python manage_historical_data.py info        # Show detailed info
    python manage_historical_data.py cleanup     # Clean old data
    python manage_historical_data.py reset       # Reset database

Author: AI Assistant
Date: 2025
"""

import sys
import os
import json
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append('src')

from historical_database import HistoricalDatabase


def show_status(db):
    """Show database status"""
    print("📊 Historical Database Status")
    print("-" * 40)

    db_info = db.get_database_info()

    if db_info['status'] == 'empty':
        print("❌ Database is empty")
        print("   Run 'update' command to populate with historical data")
    elif db_info['status'] == 'error':
        print(f"❌ Database error: {db_info['error']}")
    else:
        print("✅ Database is loaded and ready")
        print(f"   Total days: {db_info['total_days']}")
        print(f"   Total candles: {db_info['total_candles']:,}")
        print(f"   Date range: {db_info['date_range']['start']} to {db_info['date_range']['end']}")
        print(f"   Database size: {db_info['size_mb']:.2f} MB")

        if db_info.get('last_updated'):
            last_updated = datetime.fromisoformat(db_info['last_updated'])
            print(f"   Last updated: {last_updated.strftime('%Y-%m-%d %H:%M:%S')}")

        # Check if update is needed
        if db.is_update_needed():
            print("⚠️  Update recommended")
        else:
            print("✅ Database is up to date")


def update_data(db, config):
    """Update historical data"""
    print("🔄 Updating Historical Database")
    print("-" * 40)

    print("Fetching 2-week historical data (this may take a few minutes)...")

    success = db.update_historical_data(
        security_id=config['instrument']['security_id'],
        exchange_segment=config['instrument']['exchange_segment'],
        force_update=False
    )

    if success:
        print("✅ Historical data update completed successfully")
        show_status(db)
    else:
        print("❌ Historical data update failed")


def show_detailed_info(db):
    """Show detailed database information"""
    print("📋 Detailed Database Information")
    print("-" * 40)

    db_info = db.get_database_info()

    if db_info['status'] != 'loaded':
        print("❌ Database not loaded")
        return

    # Load the actual database to get more details
    database = db.load_database()

    print(f"Database file: {db_info['database_file']}")
    print(f"Total size: {db_info['size_mb']:.2f} MB")
    print(f"Total days: {len(database)}")
    print(f"Total candles: {db_info['total_candles']:,}")
    print()

    # Show per-day statistics
    print("Daily Statistics:")
    print("Date       | Candles | First Price | Last Price | Change %")
    print("-" * 60)

    for date in sorted(database.keys())[-10:]:  # Last 10 days
        daily_data = database[date]
        if daily_data:
            first_price = daily_data[0]['close']
            last_price = daily_data[-1]['close']
            change_pct = ((last_price - first_price) / first_price) * 100

            print(f"{date} | {len(daily_data):7} | {first_price:11.2f} | {last_price:10.2f} | {change_pct:7.2f}%")


def cleanup_data(db):
    """Clean up old data"""
    print("🧹 Cleaning Up Historical Database")
    print("-" * 40)

    # This will be handled automatically by the update process
    print("Cleaning up data older than 14 days...")

    # Force an update which will trigger cleanup
    success = db.update_historical_data(
        security_id="13",  # NIFTY 50
        exchange_segment="IDX_I",
        force_update=False
    )

    if success:
        print("✅ Cleanup completed")
        show_status(db)
    else:
        print("❌ Cleanup failed")


def reset_database(db):
    """Reset the database"""
    print("🔄 Resetting Historical Database")
    print("-" * 40)

    # Confirm reset
    response = input("Are you sure you want to reset the database? (y/N): ")
    if response.lower() != 'y':
        print("Reset cancelled")
        return

    try:
        # Remove database files
        if db.db_file.exists():
            db.db_file.unlink()
            print("✅ Database file removed")

        if db.metadata_file.exists():
            db.metadata_file.unlink()
            print("✅ Metadata file removed")

        print("✅ Database reset completed")
        print("Run 'update' command to rebuild the database")

    except Exception as e:
        print(f"❌ Reset failed: {e}")


def test_ema_initialization(db, config):
    """Test EMA initialization with historical data"""
    print("🧮 Testing EMA Initialization")
    print("-" * 40)

    # Get historical prices
    historical_prices = db.get_historical_prices(days=5)

    if not historical_prices:
        print("❌ No historical prices available")
        return

    print(f"Retrieved {len(historical_prices)} historical prices")
    print(f"Price range: {min(historical_prices):.2f} to {max(historical_prices):.2f}")

    # Test EMA calculation
    from ema import EMACalculator

    ema_calculator = EMACalculator(config['ema_combinations'])
    ema_calculator.load_state_from_prices("1min", historical_prices)

    current_emas = ema_calculator.get_current_ema_values("1min")

    if current_emas:
        print("✅ EMA initialization successful:")
        for ema_key, value in current_emas.items():
            print(f"   {ema_key}: {value:.2f}")
    else:
        print("❌ EMA initialization failed")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Historical Data Management Tool')
    parser.add_argument('command', choices=['status', 'update', 'info', 'cleanup', 'reset', 'test-ema'],
                       help='Command to execute')
    parser.add_argument('--config', default='config/config.json',
                       help='Path to configuration file')

    args = parser.parse_args()

    print("=" * 60)
    print("HISTORICAL DATA MANAGEMENT TOOL")
    print("=" * 60)

    try:
        # Load configuration
        with open(args.config, 'r') as f:
            config = json.load(f)

        # Create historical database instance (credentials loaded from env vars)
        db = HistoricalDatabase(
            market_hours_config=config.get('market_hours', {}),
            data_directory=config.get('data_directory', 'data')
        )

        # Execute command
        if args.command == 'status':
            show_status(db)
        elif args.command == 'update':
            update_data(db, config)
        elif args.command == 'info':
            show_detailed_info(db)
        elif args.command == 'cleanup':
            cleanup_data(db)
        elif args.command == 'reset':
            reset_database(db)
        elif args.command == 'test-ema':
            test_ema_initialization(db, config)

        print("\n" + "=" * 60)
        print("Operation completed")

    except FileNotFoundError:
        print(f"❌ Configuration file not found: {args.config}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
