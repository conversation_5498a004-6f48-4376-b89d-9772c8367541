#!/usr/bin/env python3
"""
Diagnose EMA Issue
=================

Simple diagnostic to identify the core EMA crossover detection problem.
"""

import sys
import os
from datetime import datetime

sys.path.append('src')

def test_ema_calculator():
    """
    Test the EMA calculator directly
    """
    print("🔧 TESTING EMA CALCULATOR")
    print("=" * 40)
    
    try:
        from core.ema import EMACalculator
        print("✅ EMACalculator imported successfully")
        
        # Create calculator
        calculator = EMACalculator([{"short_ema": 5, "long_ema": 10}])
        print("✅ EMACalculator created")
        
        # Test with simple price data
        test_prices = [24800, 24790, 24780, 24770, 24760, 24770, 24780, 24790, 24800, 24810]
        
        print("\n📊 Testing with simple price sequence:")
        print("Prices:", test_prices)
        
        for i, price in enumerate(test_prices):
            timestamp = datetime.now()
            emas = calculator.add_price("1min", price, timestamp)
            signals = calculator.get_crossover_signals("1min")
            
            ema5 = emas.get(5, 0)
            ema10 = emas.get(10, 0)
            
            signal_text = ""
            if signals:
                signal_text = f"🔔 {signals[0]['signal']}"
            
            print(f"Step {i+1}: Price={price}, EMA5={ema5:.2f}, EMA10={ema10:.2f} {signal_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing EMA calculator: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_crossover_logic():
    """
    Analyze the crossover detection logic
    """
    print("\n🔍 ANALYZING CROSSOVER LOGIC")
    print("=" * 40)
    
    try:
        # Check the EMA calculator source
        ema_file = "src/core/ema.py"
        if os.path.exists(ema_file):
            print(f"✅ Found EMA file: {ema_file}")
            
            # Read key parts of the crossover logic
            with open(ema_file, 'r') as f:
                content = f.read()
                
            # Look for crossover detection
            if "get_crossover_signals" in content:
                print("✅ Found crossover detection method")
            else:
                print("❌ Crossover detection method not found")
                
            if "Golden Cross" in content or "Death Cross" in content:
                print("✅ Found crossover terminology")
            else:
                print("❌ Crossover terminology not found")
                
        else:
            print(f"❌ EMA file not found: {ema_file}")
            
    except Exception as e:
        print(f"❌ Error analyzing crossover logic: {e}")

def identify_core_issues():
    """
    Identify the core issues with the current system
    """
    print("\n❌ CORE ISSUES IDENTIFIED")
    print("=" * 40)
    
    print("📋 MAIN PROBLEMS:")
    print("   1. 🚫 EMA INITIALIZATION")
    print("      • EMAs starting from scratch at market open")
    print("      • EMA5: 24229.98 vs Price: 24799.37 (570 point gap!)")
    print("      • Need 2-3 days of historical data")
    
    print("\n   2. 🚫 CROSSOVER TIMING")
    print("      • Chart shows SELL at 09:16-09:18")
    print("      • CSV shows BUY at 09:15 (wrong direction)")
    print("      • Chart shows BUY at 09:24")
    print("      • CSV shows BUY at 09:54 (30 minutes late!)")
    
    print("\n   3. 🚫 SIGNAL FREQUENCY")
    print("      • Chart shows 3 clear crossovers")
    print("      • CSV shows 42 signals (14x too many)")
    print("      • System detecting noise, not real crossovers")
    
    print("\n   4. 🚫 PRICE CONTEXT")
    print("      • Chart price range: ~24750-24850")
    print("      • CSV price range: 24799 → 23866 (massive decline)")
    print("      • Suggests wrong data or calculation errors")

def propose_immediate_fixes():
    """
    Propose immediate fixes
    """
    print("\n🛠️ IMMEDIATE FIXES NEEDED")
    print("=" * 40)
    
    print("🎯 FIX 1: HISTORICAL DATA LOADING")
    print("   • Load 2-3 days of 1-minute historical data")
    print("   • Initialize EMAs BEFORE market open")
    print("   • Ensure EMA values are close to current prices")
    
    print("\n🎯 FIX 2: CROSSOVER SENSITIVITY")
    print("   • Add minimum crossover threshold")
    print("   • Require sustained crossover (2-3 minutes)")
    print("   • Filter out micro-movements")
    
    print("\n🎯 FIX 3: DATA VALIDATION")
    print("   • Validate price data against market reality")
    print("   • Check for data feed issues")
    print("   • Ensure consistent price ranges")
    
    print("\n🎯 FIX 4: SIGNAL TIMING")
    print("   • Test with known chart data")
    print("   • Validate crossover detection against chart")
    print("   • Adjust EMA calculation parameters if needed")

def create_simple_test():
    """
    Create a simple test to verify basic functionality
    """
    print("\n🧪 SIMPLE FUNCTIONALITY TEST")
    print("=" * 40)
    
    # Simple manual EMA calculation
    print("📊 Manual EMA Test:")
    
    # Test data that should create a clear crossover
    prices = [100, 95, 90, 85, 80, 85, 90, 95, 100, 105]
    
    # Simple EMA calculation (for verification)
    ema5_values = []
    ema10_values = []
    
    alpha5 = 2 / (5 + 1)  # 0.333
    alpha10 = 2 / (10 + 1)  # 0.182
    
    ema5 = prices[0]
    ema10 = prices[0]
    
    for price in prices:
        ema5 = alpha5 * price + (1 - alpha5) * ema5
        ema10 = alpha10 * price + (1 - alpha10) * ema10
        ema5_values.append(ema5)
        ema10_values.append(ema10)
        
        crossover = ""
        if len(ema5_values) > 1:
            prev_ema5 = ema5_values[-2]
            prev_ema10 = ema10_values[-2]
            
            # Check for crossover
            if prev_ema5 <= prev_ema10 and ema5 > ema10:
                crossover = "🟢 BUY"
            elif prev_ema5 >= prev_ema10 and ema5 < ema10:
                crossover = "🔴 SELL"
        
        print(f"   Price: {price:3.0f}, EMA5: {ema5:6.2f}, EMA10: {ema10:6.2f} {crossover}")
    
    print("\n✅ This shows how crossovers should work:")
    print("   • EMA5 should cross below EMA10 → SELL signal")
    print("   • EMA5 should cross above EMA10 → BUY signal")

def main():
    """
    Main diagnostic function
    """
    print("🔍 EMA CROSSOVER DIAGNOSTIC")
    print("Identifying core issues with signal detection")
    print("=" * 60)
    
    # Test EMA calculator
    calculator_works = test_ema_calculator()
    
    # Analyze crossover logic
    analyze_crossover_logic()
    
    # Identify core issues
    identify_core_issues()
    
    # Propose fixes
    propose_immediate_fixes()
    
    # Simple test
    create_simple_test()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if calculator_works:
        print("✅ EMA Calculator is functional")
    else:
        print("❌ EMA Calculator has issues")
    
    print("\n📋 CRITICAL ISSUES:")
    print("   1. ❌ Poor EMA initialization (570 point gap)")
    print("   2. ❌ Wrong signal direction (BUY vs SELL)")
    print("   3. ❌ Wrong signal timing (30 minutes late)")
    print("   4. ❌ Too many false signals (42 vs 3)")
    
    print("\n🎯 PRIORITY FIXES:")
    print("   1. 🔧 Load historical data for EMA initialization")
    print("   2. 🔧 Fix crossover detection sensitivity")
    print("   3. 🔧 Validate against chart patterns")
    print("   4. 🔧 Test with known data sequences")

if __name__ == "__main__":
    main()
