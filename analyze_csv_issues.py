#!/usr/bin/env python3
"""
Analyze CSV Issues
==================

Simple analysis of the CSV file issues without external dependencies.
"""

import sys
import os
import csv
from datetime import datetime

sys.path.append('src')

def analyze_csv_signals():
    """
    Analyze the current CSV file to identify issues
    """
    print("🔍 ANALYZING CURRENT CSV SIGNALS")
    print("=" * 50)
    
    csv_file = "data/nifty50_ema_signals_20250530.csv"
    
    if not os.path.exists(csv_file):
        print("❌ CSV file not found")
        return
    
    try:
        with open(csv_file, 'r') as f:
            reader = csv.DictReader(f)
            signals = list(reader)
        
        print(f"📊 CSV Analysis Results:")
        print(f"   Total signals recorded: {len(signals)}")
        
        if signals:
            first_signal = signals[0]
            print(f"   First signal: {first_signal['Time']} - {first_signal['Action']}")
            print(f"   Price: {first_signal['Price']}")
            print(f"   EMA5: {first_signal['EMA5_Value']}")
            print(f"   EMA10: {first_signal['EMA10_Value']}")
            
            # Analyze morning signals (9:15-10:30)
            morning_signals = []
            for signal in signals:
                time_str = signal['Time']
                if '09:15:00' <= time_str <= '10:30:00':
                    morning_signals.append(signal)
            
            print(f"\n📈 Morning Session Analysis (9:15-10:30):")
            print(f"   Signals in morning: {len(morning_signals)}")
            
            print("\n   Morning signals:")
            for i, signal in enumerate(morning_signals[:10], 1):
                print(f"   {i:2d}. {signal['Time']} - {signal['Action']} @ {signal['Price']}")
                print(f"       EMA5: {signal['EMA5_Value']}, EMA10: {signal['EMA10_Value']}")
            
            # Compare with chart expectations
            print(f"\n🔔 CHART vs CSV COMPARISON:")
            
            expected_chart_signals = [
                ("09:15", "SELL", "EMA5 crosses below EMA10"),
                ("09:25", "BUY", "EMA5 crosses above EMA10"),
                ("09:45", "SELL", "EMA5 crosses below EMA10"),
                ("10:00", "BUY", "EMA5 crosses above EMA10"),
                ("10:15", "SELL", "EMA5 crosses below EMA10")
            ]
            
            print("   Expected from chart:")
            for time, action, desc in expected_chart_signals:
                emoji = "🟢" if action == 'BUY' else "🔴"
                print(f"     {time} - {emoji} {action} ({desc})")
            
            print("\n   Actual from CSV:")
            for signal in morning_signals[:5]:
                emoji = "🟢" if signal['Action'] == 'BUY' else "🔴"
                print(f"     {signal['Time'][:5]} - {emoji} {signal['Action']}")
            
            # Identify specific issues
            print(f"\n❌ IDENTIFIED ISSUES:")
            
            # Issue 1: First signal timing and direction
            first_time = first_signal['Time']
            first_action = first_signal['Action']
            
            if first_time == '09:15:00' and first_action == 'BUY':
                print("   1. ❌ First signal is BUY at 09:15, but chart shows SELL")
            elif first_time != '09:15:00':
                print(f"   1. ❌ First signal at {first_time}, but chart shows signal at 09:15")
            
            # Issue 2: EMA initialization
            first_price = float(first_signal['Price'])
            first_ema5 = float(first_signal['EMA5_Value'])
            first_ema10 = float(first_signal['EMA10_Value'])
            
            if abs(first_ema5 - first_price) > 500:
                print(f"   2. ❌ EMA5 ({first_ema5:.2f}) too far from price ({first_price:.2f})")
                print("      → EMAs not properly initialized with historical data")
            
            # Issue 3: Signal frequency
            if len(signals) > 15:
                print(f"   3. ❌ Too many signals ({len(signals)}) - chart shows ~5 clear crossovers")
                print("      → System detecting noise instead of real crossovers")
            
            # Issue 4: Signal pattern
            morning_pattern = [s['Action'] for s in morning_signals[:5]]
            expected_pattern = ['SELL', 'BUY', 'SELL', 'BUY', 'SELL']
            
            if morning_pattern != expected_pattern:
                print(f"   4. ❌ Signal pattern mismatch:")
                print(f"      Expected: {' → '.join(expected_pattern)}")
                print(f"      Actual:   {' → '.join(morning_pattern)}")
            
        else:
            print("❌ No signals found in CSV")
            
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")

def identify_root_causes():
    """
    Identify the root causes of the issues
    """
    print("\n🔍 ROOT CAUSE ANALYSIS")
    print("=" * 50)
    
    print("📋 PRIMARY ISSUES:")
    
    print("\n1. 🚫 INSUFFICIENT HISTORICAL DATA")
    print("   • EMAs starting from scratch at market open")
    print("   • Need 2-3 days of historical data for accurate EMAs")
    print("   • EMA5 and EMA10 values too far from current prices")
    
    print("\n2. 🚫 WRONG CROSSOVER DETECTION TIMING")
    print("   • System detecting crossovers at wrong times")
    print("   • Not matching the chart's clear crossover points")
    print("   • Missing the 09:15 SELL signal shown in chart")
    
    print("\n3. 🚫 OVERSENSITIVE SIGNAL DETECTION")
    print("   • Generating 30+ signals vs 5 expected from chart")
    print("   • Detecting noise instead of significant crossovers")
    print("   • Need better signal filtering")
    
    print("\n4. 🚫 EMA CALCULATION ACCURACY")
    print("   • EMAs not reflecting proper market context")
    print("   • Starting calculations without historical baseline")
    print("   • Need proper EMA initialization process")

def propose_solutions():
    """
    Propose solutions to fix the issues
    """
    print("\n🛠️ PROPOSED SOLUTIONS")
    print("=" * 50)
    
    print("✅ SOLUTION 1: HISTORICAL DATA INITIALIZATION")
    print("   • Load 2-3 days of 1-minute historical data before market open")
    print("   • Initialize EMAs with proper historical context")
    print("   • Ensure EMA values are close to current market prices")
    
    print("\n✅ SOLUTION 2: CHART-PATTERN VALIDATION")
    print("   • Test system with known chart data")
    print("   • Validate crossover detection against chart timestamps")
    print("   • Ensure first signal matches chart (SELL at 09:15)")
    
    print("\n✅ SOLUTION 3: SIGNAL FILTERING")
    print("   • Implement minimum price movement threshold")
    print("   • Add crossover significance validation")
    print("   • Reduce false signals from market noise")
    
    print("\n✅ SOLUTION 4: REAL-TIME VALIDATION")
    print("   • Compare detected signals with chart patterns")
    print("   • Implement crossover strength measurement")
    print("   • Add signal confidence scoring")

def main():
    """
    Main analysis function
    """
    print("🔍 CSV SIGNAL ANALYSIS")
    print("Identifying issues with current EMA crossover detection")
    print("=" * 60)
    
    # Analyze current CSV
    analyze_csv_signals()
    
    # Identify root causes
    identify_root_causes()
    
    # Propose solutions
    propose_solutions()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY")
    print("=" * 60)
    
    print("📊 CURRENT STATE:")
    print("   ❌ CSV shows wrong signals (BUY at 09:15 vs chart SELL)")
    print("   ❌ Too many signals (30+ vs expected 5)")
    print("   ❌ EMA values don't match price context")
    print("   ❌ Signal timing doesn't match chart crossovers")
    
    print("\n🎯 REQUIRED FIXES:")
    print("   1. ✅ Load historical data for proper EMA initialization")
    print("   2. ✅ Fix crossover detection to match chart pattern")
    print("   3. ✅ Reduce signal sensitivity to avoid noise")
    print("   4. ✅ Validate against known chart crossover times")
    
    print("\n🚀 NEXT STEPS:")
    print("   • Implement historical data loading in main system")
    print("   • Test with chart data to validate crossover detection")
    print("   • Adjust EMA calculation for 1-minute timeframe accuracy")
    print("   • Create chart-pattern matching validation")

if __name__ == "__main__":
    main()
