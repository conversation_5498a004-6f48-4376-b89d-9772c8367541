#!/usr/bin/env python3
"""
Test Fixed EMA System
====================

This script tests the fixes for:
1. Enhanced CSV writing with all EMA details
2. Fixed duplicate BUY signal issue
"""

import sys
import os
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append('src')

def test_enhanced_csv_logging():
    """Test enhanced CSV logging with all EMA details"""
    print("📄 Testing Enhanced CSV Logging with All EMA Details")
    print("-" * 60)
    
    try:
        from data.logger import SignalLogger
        from core.ema import EMACalculator
        
        # Create test directory
        os.makedirs("test_data", exist_ok=True)
        
        # Create logger with enhanced CSV
        logger = SignalLogger("test_data", initial_capital=100000)
        logger.recreate_daily_csv()  # Start fresh
        
        # Create EMA calculator
        ema_combinations = [
            {"short_ema": 5, "long_ema": 10},
            {"short_ema": 8, "long_ema": 21}
        ]
        calculator = EMACalculator(ema_combinations)
        
        # Generate some test data to get real EMA values
        test_prices = [24500, 24505, 24510, 24508, 24512, 24515, 24520, 24518, 24525, 24530]
        for price in test_prices:
            calculator.add_price("1min", price)
        
        # Get current EMA values
        current_emas = calculator.get_current_ema_values("1min")
        print(f"Current EMAs: {current_emas}")
        
        # Create enhanced signal with all details
        enhanced_signal = {
            'datetime': datetime.now(),
            'action': 'BUY',
            'price': 24750.50,
            'ohlc': {
                'open': 24745.00,
                'high': 24755.00,
                'low': 24740.00,
                'close': 24750.50,
                'volume': 1500
            },
            'all_emas': {
                5: 24748.25,
                8: 24746.80,
                10: 24745.80,
                12: 24744.50,
                21: 24742.30,
                26: 24741.00
            },
            'short_ema': 5,
            'long_ema': 10,
            'short_ema_value': 24748.25,
            'long_ema_value': 24745.80,
            'signal_type': '5/10 BUY',
            'pnl': 0.0
        }
        
        # Log the enhanced signal
        logger.log_signal(enhanced_signal)
        
        # Log another signal with different EMA combination
        enhanced_signal2 = {
            'datetime': datetime.now() + timedelta(minutes=5),
            'action': 'SELL',
            'price': 24735.75,
            'ohlc': {
                'open': 24750.00,
                'high': 24752.00,
                'low': 24735.00,
                'close': 24735.75,
                'volume': 1200
            },
            'all_emas': {
                5: 24740.60,
                8: 24742.15,
                10: 24743.45,
                12: 24744.20,
                21: 24745.80,
                26: 24746.50
            },
            'short_ema': 8,
            'long_ema': 21,
            'short_ema_value': 24742.15,
            'long_ema_value': 24745.80,
            'signal_type': '8/21 SELL',
            'pnl': -14.75
        }
        
        logger.log_signal(enhanced_signal2)
        
        # Get statistics
        stats = logger.get_statistics()
        print(f"✅ Enhanced signals logged. Stats: {stats}")
        
        logger.close()
        return True
        
    except Exception as e:
        print(f"❌ Enhanced CSV logging test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_duplicate_signal_fix():
    """Test that duplicate BUY signals are fixed"""
    print("\n🔔 Testing Duplicate Signal Fix")
    print("-" * 40)
    
    try:
        from core.ema import EMACalculator
        
        # Create EMA calculator
        ema_combinations = [{"short_ema": 5, "long_ema": 10}]
        calculator = EMACalculator(ema_combinations)
        
        # Generate price data that will cause EMA5 to cross above EMA10
        print("Generating price data to trigger crossover...")
        
        # Start with prices where EMA5 < EMA10
        base_prices = [24500, 24495, 24490, 24485, 24480, 24475, 24470, 24465, 24460, 24455]
        
        # Add these prices first
        for i, price in enumerate(base_prices):
            emas = calculator.add_price("1min", price)
            if i >= 9:  # After enough data
                print(f"  Price {price}: EMAs = {emas}")
        
        print("\nNow adding prices that will cause crossover...")
        
        # Now add prices that will cause EMA5 to cross above EMA10
        crossover_prices = [24470, 24485, 24500, 24515, 24530, 24545, 24560, 24575, 24590]
        
        all_signals = []
        
        for i, price in enumerate(crossover_prices):
            emas = calculator.add_price("1min", price)
            print(f"  Price {price}: EMAs = {emas}")
            
            # Check for signals
            signals = calculator.get_crossover_signals("1min")
            if signals:
                for signal in signals:
                    print(f"    🔔 {signal['signal']} Signal: {signal['short_ema']}/{signal['long_ema']} "
                          f"(Short: {signal['short_value']:.2f}, Long: {signal['long_value']:.2f})")
                    all_signals.append(signal)
            
            # Continue adding more prices after crossover to test for duplicates
            if i >= 3:  # After crossover should have happened
                # Add a few more prices to see if duplicate signals occur
                for j in range(3):
                    next_price = price + j * 2
                    emas2 = calculator.add_price("1min", next_price)
                    signals2 = calculator.get_crossover_signals("1min")
                    if signals2:
                        for signal in signals2:
                            print(f"    🔔 Additional {signal['signal']} Signal: {signal['short_ema']}/{signal['long_ema']} "
                                  f"(Short: {signal['short_value']:.2f}, Long: {signal['long_value']:.2f})")
                            all_signals.append(signal)
                break
        
        # Analyze signals
        print(f"\n📊 Signal Analysis:")
        print(f"Total signals detected: {len(all_signals)}")
        
        # Count signals by type
        buy_signals = [s for s in all_signals if s['signal'] == 'BUY']
        sell_signals = [s for s in all_signals if s['signal'] == 'SELL']
        
        print(f"BUY signals: {len(buy_signals)}")
        print(f"SELL signals: {len(sell_signals)}")
        
        # Check for duplicates
        if len(buy_signals) <= 1:
            print("✅ No duplicate BUY signals detected - Fix working!")
        else:
            print("❌ Multiple BUY signals detected - Fix may not be working")
            for i, signal in enumerate(buy_signals):
                print(f"  BUY Signal {i+1}: {signal}")
        
        return len(buy_signals) <= 1  # Should have at most 1 BUY signal
        
    except Exception as e:
        print(f"❌ Duplicate signal fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_csv_content():
    """Test and display CSV content"""
    print("\n📋 Testing CSV Content")
    print("-" * 30)
    
    try:
        csv_file = "test_data/nifty50_ema_signals_20250530.csv"
        if os.path.exists(csv_file):
            with open(csv_file, 'r') as f:
                lines = f.readlines()
            
            print(f"CSV file: {csv_file}")
            print(f"Total lines: {len(lines)}")
            print("\nCSV Headers:")
            if lines:
                headers = lines[0].strip().split(',')
                for i, header in enumerate(headers):
                    print(f"  {i+1:2d}. {header}")
            
            print("\nRecent signals:")
            for line in lines[-3:]:
                if line.strip() and not line.startswith('Date'):
                    fields = line.strip().split(',')
                    if len(fields) >= 10:
                        print(f"  {fields[1]} - {fields[2]} @ {fields[3]} "
                              f"(EMA5: {fields[9]}, EMA8: {fields[10]}, EMA10: {fields[11]})")
            
            return True
        else:
            print(f"❌ CSV file not found: {csv_file}")
            return False
            
    except Exception as e:
        print(f"❌ CSV content test failed: {e}")
        return False


def main():
    """Main test function"""
    print("=" * 70)
    print("TESTING FIXED EMA SYSTEM")
    print("=" * 70)
    
    # Test 1: Enhanced CSV logging
    test1_success = test_enhanced_csv_logging()
    
    # Test 2: Duplicate signal fix
    test2_success = test_duplicate_signal_fix()
    
    # Test 3: CSV content verification
    test3_success = test_csv_content()
    
    # Results
    print("\n" + "=" * 70)
    if test1_success and test2_success and test3_success:
        print("🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("\n✅ FIXES CONFIRMED:")
        print("  🔹 Enhanced CSV logging with all EMA details")
        print("  🔹 All EMA values (5, 8, 10, 12, 21, 26) included")
        print("  🔹 Complete OHLC data in CSV")
        print("  🔹 Signal type and combination details")
        print("  🔹 Duplicate BUY signal issue fixed")
        print("  🔹 Proper signal state tracking")
        print("  🔹 No more multiple signals for same crossover")
        
        print("\n🚀 SYSTEM IMPROVEMENTS:")
        print("  • Comprehensive CSV data logging")
        print("  • Accurate signal detection without duplicates")
        print("  • Enhanced debugging and analysis capabilities")
        print("  • Production-ready signal management")
        
    else:
        print("⚠️  SOME FIXES NEED ATTENTION")
        if not test1_success:
            print("- Enhanced CSV logging needs work")
        if not test2_success:
            print("- Duplicate signal fix needs work")
        if not test3_success:
            print("- CSV content verification failed")
    
    print("\n📁 Check test_data/ directory for updated CSV files")
    print("🔧 System ready with all fixes applied!")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
