#!/usr/bin/env python3
"""
State Manager
=============

This module manages EMA state persistence and recovery across system restarts.
It stores candle data and EMA calculations to enable seamless continuation
of signal generation within the same trading day.

Key Features:
- Store all candle data for the current trading day
- Reconstruct EMA state from stored candles
- Maintain signal continuity across restarts
- Handle daily reset at market open

Author: AI Assistant
Date: 2025
"""

import os
import json
import pickle
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path


class StateManager:
    """
    Manages EMA state persistence and recovery
    """
    
    def __init__(self, data_directory: str = "data"):
        """
        Initialize state manager
        
        Args:
            data_directory: Directory to store state files
        """
        self.data_directory = Path(data_directory)
        self.data_directory.mkdir(exist_ok=True)
        
        # Current session info
        self.current_date = None
        self.candle_data = {}  # {timeframe: [candle_dict, ...]}
        self.ema_state = {}    # {timeframe: {ema_period: [values]}}
        
        self.logger = logging.getLogger(__name__)
        
    def get_state_file_path(self, date_str: str) -> Path:
        """Get state file path for a specific date"""
        return self.data_directory / f"ema_state_{date_str}.pkl"
    
    def save_candle(self, timeframe: str, candle_dict: Dict):
        """
        Save a completed candle for state persistence
        
        Args:
            timeframe: Timeframe identifier
            candle_dict: Candle data dictionary
        """
        try:
            # Initialize timeframe if needed
            if timeframe not in self.candle_data:
                self.candle_data[timeframe] = []
            
            # Add candle to storage
            self.candle_data[timeframe].append(candle_dict)
            
            # Keep only recent candles (last 100 for memory efficiency)
            if len(self.candle_data[timeframe]) > 100:
                self.candle_data[timeframe] = self.candle_data[timeframe][-100:]
            
            self.logger.debug(f"Saved candle for {timeframe}: {candle_dict.get('close', 0):.2f}")
            
        except Exception as e:
            self.logger.error(f"Error saving candle: {e}")
    
    def save_ema_state(self, timeframe: str, ema_values: Dict[str, List[float]]):
        """
        Save current EMA state
        
        Args:
            timeframe: Timeframe identifier
            ema_values: Dictionary of EMA values {period: [values]}
        """
        try:
            self.ema_state[timeframe] = {}
            
            for period, values in ema_values.items():
                # Keep only recent values (last 50 for memory efficiency)
                self.ema_state[timeframe][period] = values[-50:] if len(values) > 50 else values.copy()
            
            self.logger.debug(f"Saved EMA state for {timeframe}")
            
        except Exception as e:
            self.logger.error(f"Error saving EMA state: {e}")
    
    def save_daily_state(self, date_str: str = None):
        """
        Save complete daily state to file
        
        Args:
            date_str: Date string (YYYYMMDD), defaults to today
        """
        try:
            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")
            
            state_data = {
                'date': date_str,
                'timestamp': datetime.now().isoformat(),
                'candle_data': self.candle_data,
                'ema_state': self.ema_state
            }
            
            state_file = self.get_state_file_path(date_str)
            
            with open(state_file, 'wb') as f:
                pickle.dump(state_data, f)
            
            self.logger.info(f"Saved daily state to {state_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving daily state: {e}")
    
    def load_daily_state(self, date_str: str = None) -> bool:
        """
        Load daily state from file
        
        Args:
            date_str: Date string (YYYYMMDD), defaults to today
            
        Returns:
            True if state was loaded successfully
        """
        try:
            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")
            
            state_file = self.get_state_file_path(date_str)
            
            if not state_file.exists():
                self.logger.info(f"No existing state file for {date_str}")
                return False
            
            with open(state_file, 'rb') as f:
                state_data = pickle.load(f)
            
            # Validate state data
            if state_data.get('date') != date_str:
                self.logger.warning(f"State file date mismatch: {state_data.get('date')} != {date_str}")
                return False
            
            # Load state
            self.current_date = date_str
            self.candle_data = state_data.get('candle_data', {})
            self.ema_state = state_data.get('ema_state', {})
            
            # Log loaded state info
            total_candles = sum(len(candles) for candles in self.candle_data.values())
            total_emas = sum(len(emas) for emas in self.ema_state.values())
            
            self.logger.info(f"Loaded daily state for {date_str}: {total_candles} candles, {total_emas} EMA series")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading daily state: {e}")
            return False
    
    def get_historical_candles(self, timeframe: str) -> List[Dict]:
        """
        Get historical candles for a timeframe
        
        Args:
            timeframe: Timeframe identifier
            
        Returns:
            List of candle dictionaries
        """
        return self.candle_data.get(timeframe, []).copy()
    
    def get_historical_prices(self, timeframe: str) -> List[float]:
        """
        Get historical close prices for EMA reconstruction
        
        Args:
            timeframe: Timeframe identifier
            
        Returns:
            List of close prices in chronological order
        """
        candles = self.get_historical_candles(timeframe)
        prices = []
        
        for candle in candles:
            close_price = candle.get('close')
            if close_price is not None:
                prices.append(float(close_price))
        
        return prices
    
    def get_ema_state(self, timeframe: str) -> Dict[str, List[float]]:
        """
        Get stored EMA state for a timeframe
        
        Args:
            timeframe: Timeframe identifier
            
        Returns:
            Dictionary of EMA values {period: [values]}
        """
        return self.ema_state.get(timeframe, {}).copy()
    
    def reset_daily_state(self):
        """Reset state for new trading day"""
        self.candle_data.clear()
        self.ema_state.clear()
        self.current_date = None
        self.logger.info("Daily state reset")
    
    def cleanup_old_state_files(self, days_to_keep: int = 7):
        """
        Clean up old state files
        
        Args:
            days_to_keep: Number of days of state files to keep
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_date.strftime("%Y%m%d")
            
            for state_file in self.data_directory.glob("ema_state_*.pkl"):
                try:
                    # Extract date from filename
                    filename = state_file.name
                    date_part = filename.replace("ema_state_", "").replace(".pkl", "")
                    
                    if len(date_part) == 8 and date_part.isdigit():
                        if date_part < cutoff_str:
                            state_file.unlink()
                            self.logger.info(f"Cleaned up old state file: {state_file}")
                            
                except Exception as e:
                    self.logger.warning(f"Error processing state file {state_file}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error cleaning up old state files: {e}")
    
    def get_state_summary(self) -> Dict:
        """Get summary of current state"""
        summary = {
            'current_date': self.current_date,
            'timeframes': list(self.candle_data.keys()),
            'total_candles': sum(len(candles) for candles in self.candle_data.values()),
            'ema_timeframes': list(self.ema_state.keys()),
            'total_ema_series': sum(len(emas) for emas in self.ema_state.values())
        }
        
        # Add per-timeframe details
        for tf in self.candle_data:
            candles = self.candle_data[tf]
            if candles:
                summary[f'{tf}_candles'] = len(candles)
                summary[f'{tf}_latest_price'] = candles[-1].get('close', 0)
        
        return summary
