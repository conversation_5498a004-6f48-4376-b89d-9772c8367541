#!/usr/bin/env python3
"""
Custom TA-Lib Alternative
=========================

This module provides a custom implementation of TA-Lib functions using NumPy
for fast and robust technical analysis calculations when TA-Lib is not available.

This gives us the performance benefits without the installation complexity.

Key Features:
- Fast EMA calculations using NumPy
- Vectorized operations for better performance
- Drop-in replacement for TA-Lib EMA function
- No external dependencies beyond NumPy

Author: AI Assistant
Date: 2025
"""

import logging
from typing import List, Optional, Union
import math

# Try to import NumPy for fast calculations
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    np = None
    NUMPY_AVAILABLE = False

logger = logging.getLogger(__name__)


class CustomTALib:
    """
    Custom TA-Lib alternative implementation
    
    Provides fast and robust technical analysis functions using NumPy
    when available, with pure Python fallback.
    """
    
    @staticmethod
    def EMA(prices: Union[List[float], 'np.ndarray'], timeperiod: int = 30) -> Union[List[float], 'np.ndarray']:
        """
        Calculate Exponential Moving Average (EMA)
        
        This is a drop-in replacement for talib.EMA() function.
        
        Args:
            prices: Array of prices (close prices typically)
            timeperiod: Number of periods for EMA calculation
            
        Returns:
            Array of EMA values (same type as input)
        """
        if NUMPY_AVAILABLE and isinstance(prices, np.ndarray):
            return CustomTALib._ema_numpy(prices, timeperiod)
        else:
            # Convert to list if numpy array but numpy not available
            if hasattr(prices, 'tolist'):
                prices = prices.tolist()
            return CustomTALib._ema_python(prices, timeperiod)
    
    @staticmethod
    def _ema_numpy(prices: 'np.ndarray', timeperiod: int) -> 'np.ndarray':
        """
        Fast EMA calculation using NumPy vectorized operations
        
        Args:
            prices: NumPy array of prices
            timeperiod: EMA period
            
        Returns:
            NumPy array of EMA values
        """
        try:
            # Calculate smoothing factor
            alpha = 2.0 / (timeperiod + 1)
            
            # Initialize result array with NaN
            ema = np.full_like(prices, np.nan, dtype=np.float64)
            
            # Need at least timeperiod values to start
            if len(prices) < timeperiod:
                return ema
            
            # Calculate initial SMA for the first EMA value
            sma = np.mean(prices[:timeperiod])
            ema[timeperiod - 1] = sma
            
            # Calculate EMA for remaining values using vectorized operations
            for i in range(timeperiod, len(prices)):
                ema[i] = alpha * prices[i] + (1 - alpha) * ema[i - 1]
            
            logger.debug(f"Calculated EMA{timeperiod} using NumPy for {len(prices)} prices")
            return ema
            
        except Exception as e:
            logger.error(f"Error in NumPy EMA calculation: {e}")
            # Fallback to Python implementation
            return CustomTALib._ema_python(prices.tolist(), timeperiod)
    
    @staticmethod
    def _ema_python(prices: List[float], timeperiod: int) -> List[float]:
        """
        Pure Python EMA calculation (fallback implementation)
        
        Args:
            prices: List of prices
            timeperiod: EMA period
            
        Returns:
            List of EMA values
        """
        try:
            # Calculate smoothing factor
            alpha = 2.0 / (timeperiod + 1)
            
            # Initialize result list with None
            ema = [float('nan')] * len(prices)
            
            # Need at least timeperiod values to start
            if len(prices) < timeperiod:
                return ema
            
            # Calculate initial SMA for the first EMA value
            sma = sum(prices[:timeperiod]) / timeperiod
            ema[timeperiod - 1] = sma
            
            # Calculate EMA for remaining values
            for i in range(timeperiod, len(prices)):
                ema[i] = alpha * prices[i] + (1 - alpha) * ema[i - 1]
            
            logger.debug(f"Calculated EMA{timeperiod} using Python for {len(prices)} prices")
            return ema
            
        except Exception as e:
            logger.error(f"Error in Python EMA calculation: {e}")
            return [float('nan')] * len(prices)
    
    @staticmethod
    def SMA(prices: Union[List[float], 'np.ndarray'], timeperiod: int = 30) -> Union[List[float], 'np.ndarray']:
        """
        Calculate Simple Moving Average (SMA)
        
        Args:
            prices: Array of prices
            timeperiod: Number of periods for SMA calculation
            
        Returns:
            Array of SMA values
        """
        if NUMPY_AVAILABLE and isinstance(prices, np.ndarray):
            return CustomTALib._sma_numpy(prices, timeperiod)
        else:
            if hasattr(prices, 'tolist'):
                prices = prices.tolist()
            return CustomTALib._sma_python(prices, timeperiod)
    
    @staticmethod
    def _sma_numpy(prices: 'np.ndarray', timeperiod: int) -> 'np.ndarray':
        """Fast SMA calculation using NumPy"""
        try:
            # Initialize result array with NaN
            sma = np.full_like(prices, np.nan, dtype=np.float64)
            
            # Calculate SMA using rolling window
            for i in range(timeperiod - 1, len(prices)):
                sma[i] = np.mean(prices[i - timeperiod + 1:i + 1])
            
            return sma
            
        except Exception as e:
            logger.error(f"Error in NumPy SMA calculation: {e}")
            return CustomTALib._sma_python(prices.tolist(), timeperiod)
    
    @staticmethod
    def _sma_python(prices: List[float], timeperiod: int) -> List[float]:
        """Pure Python SMA calculation"""
        try:
            sma = [float('nan')] * len(prices)
            
            for i in range(timeperiod - 1, len(prices)):
                sma[i] = sum(prices[i - timeperiod + 1:i + 1]) / timeperiod
            
            return sma
            
        except Exception as e:
            logger.error(f"Error in Python SMA calculation: {e}")
            return [float('nan')] * len(prices)


# Create a module-level instance for easy access
custom_talib = CustomTALib()

# Provide functions that match TA-Lib interface
def EMA(prices, timeperiod=30):
    """Exponential Moving Average - matches talib.EMA interface"""
    return custom_talib.EMA(prices, timeperiod)

def SMA(prices, timeperiod=30):
    """Simple Moving Average - matches talib.SMA interface"""
    return custom_talib.SMA(prices, timeperiod)


# Test function
def test_custom_talib():
    """Test the custom TA-Lib implementation"""
    print("Testing Custom TA-Lib Implementation...")
    
    # Test data
    test_prices = [100.0, 101.0, 102.0, 101.5, 103.0, 102.5, 104.0, 103.5, 105.0, 104.5]
    
    # Test EMA calculation
    ema_result = EMA(test_prices, timeperiod=5)
    print(f"EMA5 result: {ema_result}")
    
    # Test SMA calculation
    sma_result = SMA(test_prices, timeperiod=5)
    print(f"SMA5 result: {sma_result}")
    
    # Test with NumPy if available
    if NUMPY_AVAILABLE:
        np_prices = np.array(test_prices)
        ema_np = EMA(np_prices, timeperiod=5)
        print(f"EMA5 NumPy result: {ema_np}")
    
    print("Custom TA-Lib test completed!")


if __name__ == "__main__":
    test_custom_talib()
