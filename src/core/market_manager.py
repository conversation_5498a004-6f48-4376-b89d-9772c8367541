#!/usr/bin/env python3
"""
Market Manager Module
====================

This module manages market hours, trading sessions, and ensures complete
day data capture for EMA calculations regardless of when the user logs in.

Key Features:
- NSE market hours management (9:15 AM to 3:30 PM)
- Complete day data reconstruction from market open
- No duplicate data handling
- Market state tracking
- Session management

Author: AI Assistant
Date: 2025
"""

import logging
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import json


class MarketManager:
    """
    Market Manager for NSE trading hours and session management
    
    Handles market timing, session state, and ensures complete day data
    is available for EMA calculations regardless of login time.
    """
    
    def __init__(self, data_directory: str = "data"):
        """
        Initialize Market Manager
        
        Args:
            data_directory: Directory for storing market data and state
        """
        self.data_directory = Path(data_directory)
        self.data_directory.mkdir(exist_ok=True)
        
        # NSE Market Hours (Indian Standard Time)
        self.market_open_time = time(9, 15)    # 9:15 AM
        self.market_close_time = time(15, 30)  # 3:30 PM
        
        # Pre-market and post-market hours
        self.pre_market_start = time(9, 0)     # 9:00 AM
        self.post_market_end = time(16, 0)     # 4:00 PM
        
        # Market holidays (can be loaded from external source)
        self.market_holidays = set()  # Will be populated with holiday dates
        
        # Current session state
        self.current_session_date = None
        self.session_start_time = None
        self.session_data_points = 0
        self.last_data_timestamp = None
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Market Manager initialized for NSE trading hours")
        
        # Load or create session state
        self._load_session_state()
    
    def is_market_open(self, check_time: datetime = None) -> bool:
        """
        Check if market is currently open
        
        Args:
            check_time: Time to check (default: current time)
            
        Returns:
            True if market is open
        """
        if check_time is None:
            check_time = datetime.now()
        
        # Check if it's a weekday (Monday=0, Sunday=6)
        if check_time.weekday() >= 5:  # Saturday or Sunday
            return False
        
        # Check if it's a market holiday
        if check_time.date() in self.market_holidays:
            return False
        
        # Check market hours
        current_time = check_time.time()
        return self.market_open_time <= current_time <= self.market_close_time
    
    def is_trading_day(self, check_date: datetime = None) -> bool:
        """
        Check if given date is a trading day
        
        Args:
            check_date: Date to check (default: today)
            
        Returns:
            True if it's a trading day
        """
        if check_date is None:
            check_date = datetime.now()
        
        # Check weekday
        if check_date.weekday() >= 5:
            return False
        
        # Check holidays
        return check_date.date() not in self.market_holidays
    
    def get_market_session_times(self, session_date: datetime = None) -> Tuple[datetime, datetime]:
        """
        Get market open and close times for a session
        
        Args:
            session_date: Date for the session (default: today)
            
        Returns:
            Tuple of (market_open_datetime, market_close_datetime)
        """
        if session_date is None:
            session_date = datetime.now()
        
        market_open = datetime.combine(session_date.date(), self.market_open_time)
        market_close = datetime.combine(session_date.date(), self.market_close_time)
        
        return market_open, market_close
    
    def get_minutes_since_market_open(self, current_time: datetime = None) -> int:
        """
        Get number of minutes since market opened today
        
        Args:
            current_time: Current time (default: now)
            
        Returns:
            Minutes since market open (negative if before market open)
        """
        if current_time is None:
            current_time = datetime.now()
        
        market_open, _ = self.get_market_session_times(current_time)
        
        if current_time < market_open:
            return -1  # Market not yet open
        
        delta = current_time - market_open
        return int(delta.total_seconds() / 60)
    
    def get_expected_data_points(self, current_time: datetime = None) -> int:
        """
        Get expected number of 1-minute data points since market open
        
        Args:
            current_time: Current time (default: now)
            
        Returns:
            Expected number of 1-minute data points
        """
        minutes = self.get_minutes_since_market_open(current_time)
        return max(0, minutes)
    
    def should_fetch_historical_data(self, current_time: datetime = None) -> bool:
        """
        Determine if we need to fetch historical data to fill gaps
        
        Args:
            current_time: Current time (default: now)
            
        Returns:
            True if historical data fetch is needed
        """
        if current_time is None:
            current_time = datetime.now()
        
        # If market is not open, no need to fetch
        if not self.is_market_open(current_time):
            return False
        
        expected_points = self.get_expected_data_points(current_time)
        
        # If we have significantly fewer data points than expected, fetch historical
        return self.session_data_points < (expected_points * 0.8)  # 80% threshold
    
    def get_missing_data_timerange(self, current_time: datetime = None) -> Tuple[datetime, datetime]:
        """
        Get the time range for missing data that needs to be fetched
        
        Args:
            current_time: Current time (default: now)
            
        Returns:
            Tuple of (start_time, end_time) for missing data
        """
        if current_time is None:
            current_time = datetime.now()
        
        market_open, _ = self.get_market_session_times(current_time)
        
        # If we have last data timestamp, start from there
        if self.last_data_timestamp:
            start_time = self.last_data_timestamp + timedelta(minutes=1)
        else:
            start_time = market_open
        
        # End time is current time (rounded to minute)
        end_time = current_time.replace(second=0, microsecond=0)
        
        return start_time, end_time
    
    def update_session_data(self, data_timestamp: datetime, data_count: int = 1):
        """
        Update session data tracking
        
        Args:
            data_timestamp: Timestamp of the data point
            data_count: Number of data points added
        """
        today = datetime.now().date()
        
        # Check if this is a new session
        if self.current_session_date != today:
            self._start_new_session(today)
        
        # Update tracking
        self.session_data_points += data_count
        self.last_data_timestamp = data_timestamp
        
        # Save state
        self._save_session_state()
    
    def _start_new_session(self, session_date):
        """Start a new trading session"""
        self.current_session_date = session_date
        self.session_start_time = datetime.now()
        self.session_data_points = 0
        self.last_data_timestamp = None
        
        self.logger.info(f"Started new trading session for {session_date}")
    
    def _load_session_state(self):
        """Load session state from file"""
        try:
            state_file = self.data_directory / "market_session_state.json"
            if state_file.exists():
                with open(state_file, 'r') as f:
                    state = json.load(f)
                
                # Parse dates
                if state.get('current_session_date'):
                    self.current_session_date = datetime.fromisoformat(state['current_session_date']).date()
                
                if state.get('session_start_time'):
                    self.session_start_time = datetime.fromisoformat(state['session_start_time'])
                
                if state.get('last_data_timestamp'):
                    self.last_data_timestamp = datetime.fromisoformat(state['last_data_timestamp'])
                
                self.session_data_points = state.get('session_data_points', 0)
                
                # Check if it's a new day
                today = datetime.now().date()
                if self.current_session_date != today:
                    self._start_new_session(today)
                
                self.logger.info(f"Loaded session state: {self.session_data_points} data points")
        
        except Exception as e:
            self.logger.warning(f"Could not load session state: {e}")
            self._start_new_session(datetime.now().date())
    
    def _save_session_state(self):
        """Save session state to file"""
        try:
            state = {
                'current_session_date': self.current_session_date.isoformat() if self.current_session_date else None,
                'session_start_time': self.session_start_time.isoformat() if self.session_start_time else None,
                'last_data_timestamp': self.last_data_timestamp.isoformat() if self.last_data_timestamp else None,
                'session_data_points': self.session_data_points
            }
            
            state_file = self.data_directory / "market_session_state.json"
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
        
        except Exception as e:
            self.logger.error(f"Could not save session state: {e}")
    
    def get_session_summary(self) -> Dict:
        """
        Get current session summary
        
        Returns:
            Dictionary with session information
        """
        current_time = datetime.now()
        market_open, market_close = self.get_market_session_times(current_time)
        
        return {
            'session_date': self.current_session_date.isoformat() if self.current_session_date else None,
            'market_open': market_open.isoformat(),
            'market_close': market_close.isoformat(),
            'is_market_open': self.is_market_open(current_time),
            'minutes_since_open': self.get_minutes_since_market_open(current_time),
            'expected_data_points': self.get_expected_data_points(current_time),
            'actual_data_points': self.session_data_points,
            'data_completeness': (self.session_data_points / max(1, self.get_expected_data_points(current_time))) * 100,
            'last_data_timestamp': self.last_data_timestamp.isoformat() if self.last_data_timestamp else None,
            'should_fetch_historical': self.should_fetch_historical_data(current_time)
        }
    
    def add_market_holiday(self, holiday_date: datetime):
        """Add a market holiday"""
        self.market_holidays.add(holiday_date.date())
        self.logger.info(f"Added market holiday: {holiday_date.date()}")
    
    def load_market_holidays(self, holidays: List[datetime]):
        """Load list of market holidays"""
        self.market_holidays = {h.date() for h in holidays}
        self.logger.info(f"Loaded {len(self.market_holidays)} market holidays")
