#!/usr/bin/env python3
"""
Direct EMA Test
===============

Direct test of EMA crossover functionality without complex data generation.
"""

import sys
import os
from datetime import datetime, timedelta

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
sys.path.insert(0, src_path)

def main():
    print("🎯 DIRECT EMA CROSSOVER TEST")
    print("=" * 40)
    
    try:
        # Import the EMA calculator
        from core.ema import EMACalculator
        print("✅ Successfully imported EMACalculator")
        
        # Create calculator for 5/10 EMA crossover
        calculator = EMACalculator([{"short_ema": 5, "long_ema": 10}])
        print("✅ EMACalculator created for 5/10 crossover")
        
        # Test with simple price sequence
        print("\n📊 Testing with price sequence designed to create crossovers:")
        
        # Prices that should create crossovers
        # Start with declining prices, then recovery
        prices = [
            # Initial prices to establish EMAs (need at least 10 for EMA10)
            100, 99, 98, 97, 96, 95, 94, 93, 92, 91,  # 10 prices - declining
            90, 89, 88, 87, 86,  # Continue decline (should create SELL signal)
            87, 88, 89, 90, 91,  # Start recovery
            92, 93, 94, 95, 96,  # Continue recovery (should create BUY signal)
            95, 94, 93, 92, 91,  # Another decline (should create SELL signal)
        ]
        
        signals_detected = []
        base_time = datetime.now()
        
        print("\nStep | Price | EMA5  | EMA10 | Signal")
        print("-" * 40)
        
        for i, price in enumerate(prices):
            timestamp = base_time + timedelta(minutes=i)
            
            # Add price to calculator
            emas = calculator.add_price("1min", price, timestamp)
            
            # Check for crossover signals
            signals = calculator.get_crossover_signals("1min")
            
            # Get EMA values
            ema5 = emas.get(5, 0)
            ema10 = emas.get(10, 0)
            
            signal_text = ""
            if signals:
                signal = signals[0]
                signal_text = f"{signal['signal']}"
                signals_detected.append({
                    'step': i + 1,
                    'price': price,
                    'signal': signal['signal'],
                    'ema5': ema5,
                    'ema10': ema10
                })
            
            # Show every step after EMA10 is initialized
            if i >= 9:  # After 10 prices
                print(f"{i+1:4d} | {price:5.0f} | {ema5:5.2f} | {ema10:5.2f} | {signal_text}")
        
        # Results
        print("\n" + "=" * 40)
        print("📊 CROSSOVER RESULTS")
        print("=" * 40)
        
        if signals_detected:
            print("🔔 CROSSOVER SIGNALS DETECTED:")
            for signal in signals_detected:
                emoji = "🟢" if signal['signal'] == 'BUY' else "🔴"
                print(f"  Step {signal['step']}: {emoji} {signal['signal']} at price {signal['price']}")
                print(f"    EMA5: {signal['ema5']:.2f}, EMA10: {signal['ema10']:.2f}")
            
            print(f"\n✅ SUCCESS: {len(signals_detected)} crossover signals detected!")
            
            # Verify signal logic
            for signal in signals_detected:
                if signal['signal'] == 'BUY':
                    if signal['ema5'] > signal['ema10']:
                        print(f"✅ BUY signal correct: EMA5 ({signal['ema5']:.2f}) > EMA10 ({signal['ema10']:.2f})")
                    else:
                        print(f"❌ BUY signal incorrect: EMA5 ({signal['ema5']:.2f}) <= EMA10 ({signal['ema10']:.2f})")
                elif signal['signal'] == 'SELL':
                    if signal['ema5'] < signal['ema10']:
                        print(f"✅ SELL signal correct: EMA5 ({signal['ema5']:.2f}) < EMA10 ({signal['ema10']:.2f})")
                    else:
                        print(f"❌ SELL signal incorrect: EMA5 ({signal['ema5']:.2f}) >= EMA10 ({signal['ema10']:.2f})")
            
            print("\n🎯 CONCLUSION:")
            print("✅ EMA crossover detection is working correctly!")
            print("✅ The system can detect both Golden Cross (BUY) and Death Cross (SELL)")
            print("✅ Signal logic is accurate")
            print("✅ Ready for 1-minute timeframe trading")
            
        else:
            print("❌ No crossover signals detected")
            print("This might indicate:")
            print("  - EMA calculation issue")
            print("  - Crossover detection logic problem")
            print("  - Insufficient price movement to create crossovers")
            
            # Debug info
            latest_emas = calculator.get_latest_emas("1min")
            print(f"\nDebug - Latest EMAs: {latest_emas}")
            
            stats = calculator.get_statistics("1min")
            print(f"Debug - Statistics: {stats}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
