#!/usr/bin/env python3
"""
Simple 1-Minute EMA Crossover Test
=================================

Direct test of 1-minute EMA crossover detection with minimal data generation.
"""

import sys
from datetime import datetime, timedelta

sys.path.append('src')

def test_simple_1min_crossovers():
    """
    Simple test with manually crafted 1-minute data to verify crossovers
    """
    print("🎯 SIMPLE 1-MINUTE EMA CROSSOVER TEST")
    print("=" * 50)
    
    try:
        from core.ema import EMACalculator
        
        # Create calculator
        calculator = EMACalculator([{"short_ema": 5, "long_ema": 10}])
        print("✅ EMACalculator created")
        
        # Create simple 1-minute data that should generate crossovers
        base_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
        
        # Price data designed to create clear crossovers
        # Start high, go down (SELL), then up (BUY), then down again (SELL)
        price_data = [
            # Initial prices to establish EMAs
            24850, 24845, 24840, 24835, 24830,  # 5 prices
            24825, 24820, 24815, 24810, 24805,  # 10 prices total
            24800, 24795, 24790, 24785, 24780,  # 15 prices
            
            # Now create crossover pattern
            24775, 24770, 24765, 24760, 24755,  # Continued decline (should create SELL)
            24750, 24745, 24740, 24735, 24730,  # More decline
            
            # Recovery (should create BUY)
            24735, 24740, 24745, 24750, 24755,  # Recovery starts
            24760, 24765, 24770, 24775, 24780,  # Continued recovery
            24785, 24790, 24795, 24800, 24805,  # Strong recovery
            
            # Another decline (should create SELL)
            24800, 24795, 24790, 24785, 24780,  # Start decline
            24775, 24770, 24765, 24760, 24755,  # Continue decline
        ]
        
        detected_signals = []
        
        print("\nProcessing 1-minute price data:")
        print("Minute | Price   | EMA5    | EMA10   | Signal")
        print("-" * 50)
        
        for i, price in enumerate(price_data):
            timestamp = base_time + timedelta(minutes=i)
            
            # Add price for 1-minute timeframe
            emas = calculator.add_price("1min", price, timestamp)
            
            # Check for crossover signals
            signals = calculator.get_crossover_signals("1min")
            
            ema5 = emas.get(5, 0)
            ema10 = emas.get(10, 0)
            
            signal_text = ""
            if signals:
                signal = signals[0]
                signal_text = f"🔔 {signal['signal']}"
                detected_signals.append({
                    'minute': i + 1,
                    'time': timestamp.strftime('%H:%M'),
                    'signal': signal['signal'],
                    'price': price,
                    'ema5': ema5,
                    'ema10': ema10
                })
            
            # Show progress (every 5th minute or when signal detected)
            if i % 5 == 0 or signals:
                print(f"{i+1:6d} | {price:7.0f} | {ema5:7.2f} | {ema10:7.2f} | {signal_text}")
        
        return detected_signals
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """
    Main test function
    """
    print("🎯 1-MINUTE EMA CROSSOVER VERIFICATION")
    print("Testing with simple manually crafted 1-minute data")
    print("=" * 60)
    
    # Run the test
    signals = test_simple_1min_crossovers()
    
    # Results
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    if signals:
        print("🔔 CROSSOVER SIGNALS DETECTED:")
        for signal in signals:
            emoji = "🟢" if signal['signal'] == 'BUY' else "🔴"
            print(f"  Minute {signal['minute']} ({signal['time']}) - {emoji} {signal['signal']}")
            print(f"    Price: {signal['price']}, EMA5: {signal['ema5']:.2f}, EMA10: {signal['ema10']:.2f}")
        
        print(f"\n✅ SUCCESS: {len(signals)} crossover signals detected!")
        print("✅ 1-minute timeframe EMA crossover detection is working!")
        
        # Pattern analysis
        pattern = ' → '.join([s['signal'] for s in signals])
        print(f"✅ Signal pattern: {pattern}")
        
    else:
        print("❌ No crossover signals detected")
        print("   This indicates an issue with the crossover detection logic")
    
    print("\n🎯 CONCLUSION:")
    if signals:
        print("The Enhanced EMA Trading System successfully detects crossovers")
        print("on 1-minute timeframe data, confirming it will work with real")
        print("1-minute market data for intraday trading signals.")
    else:
        print("There may be an issue with EMA calculation or crossover logic")
        print("that needs to be investigated.")

if __name__ == "__main__":
    main()
