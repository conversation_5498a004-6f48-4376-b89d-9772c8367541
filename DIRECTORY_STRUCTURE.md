# 📁 Directory Structure

This document outlines the organized directory structure of the AATHMA NIRBHAYA trading system.

## 🏗️ Project Structure

```
AATHMA_NIRBHAYA/
├── 📄 README.md                    # Main project documentation
├── 📄 .env                         # Environment variables (credentials)
├── 📄 .env.example                 # Environment template
├── 📄 .gitignore                   # Git ignore rules
├── 📄 requirements.txt             # Python dependencies
├── 📄 requirements-dev.txt         # Development dependencies
├── 📄 setup.py                     # Package setup
├── 📄 ema_daemon.py                # System daemon controller
├── 📄 manage_historical_data.py    # Historical data management
│
├── 📁 config/                      # Configuration files
│   ├── config.json                 # Main configuration (no credentials)
│   └── config.json.example         # Configuration template
│
├── 📁 src/                         # Source code
│   ├── __init__.py
│   ├── main.py                     # Main application entry point
│   │
│   ├── 📁 core/                    # Core trading components
│   │   ├── __init__.py
│   │   ├── ema.py                  # EMA calculation engine
│   │   ├── market_feed.py          # DhanHQ market data feed
│   │   └── strategy.py             # Trading strategy logic
│   │
│   ├── 📁 data/                    # Data management
│   │   ├── __init__.py
│   │   ├── historical_data.py      # Historical data recovery
│   │   ├── historical_database.py  # Historical data management
│   │   └── logger.py               # Signal logging
│   │
│   └── 📁 utils/                   # Utility modules
│       ├── __init__.py
│       ├── market_hours.py         # Market timing utilities
│       └── state_manager.py        # State persistence
│
├── 📁 data/                        # Data storage
│   ├── 📁 historical/              # Historical market data
│   │   ├── metadata.json
│   │   ├── nifty50_historical.csv
│   │   └── nifty50_historical.pkl
│   ├── 📁 signals/                 # Signal archives
│   ├── 📁 state/                   # System state files
│   ├── 📁 logs/                    # Data-related logs
│   └── *.csv                       # Daily signal files
│
├── 📁 logs/                        # System logs
│   ├── ema_daemon.log              # Daemon logs
│   └── ema_system_*.log            # Daily system logs
│
├── 📁 tests/                       # Test suite
│   ├── __init__.py
│   ├── test_complete_system.py     # Full system tests
│   ├── test_historical_database.py # Database tests
│   ├── test_historical_recovery.py # Recovery tests
│   └── *.py                        # Other test modules
│
├── 📁 docs/                        # Documentation
│   ├── ARCHITECTURE.md             # System architecture
│   └── DEPLOYMENT.md               # Deployment guide
│
├── 📁 scripts/                     # Utility scripts
│   └── demo_enhanced_system.py     # Demo scripts
│
├── 📁 notebooks/                   # Jupyter notebooks
│   └── (analysis notebooks)
│
├── 📁 test_data/                   # Test data files
│   └── *.csv                       # Test signal data
│
└── 📁 stock/                       # Virtual environment
    ├── bin/                        # Python binaries
    ├── lib/                        # Python libraries
    └── pyvenv.cfg                  # Virtual env config
```

## 🔑 Key Files

### Core Application
- **`src/main.py`** - Main application entry point
- **`ema_daemon.py`** - System daemon for background operation
- **`manage_historical_data.py`** - Historical data management tool

### Configuration
- **`.env`** - Environment variables (credentials) - **NOT in git**
- **`.env.example`** - Template for environment setup
- **`config/config.json`** - Main configuration (no sensitive data)

### Data Files
- **`data/nifty50_ema_signals_*.csv`** - Daily trading signals
- **`data/historical/`** - Historical market data storage
- **`logs/ema_daemon.log`** - System operation logs

## 🔒 Security Notes

- **`.env`** contains sensitive credentials and is excluded from git
- **`config/config.json`** contains only non-sensitive configuration
- All credentials are loaded from environment variables
- **`__pycache__`** directories are cleaned and ignored

## 🧪 Testing

- All test files are organized in the **`tests/`** directory
- Test data is stored in **`test_data/`** directory
- Run tests from the project root directory

## 📊 Data Organization

- **Live data**: `data/` directory
- **Historical data**: `data/historical/` subdirectory  
- **Logs**: `logs/` directory
- **Test data**: `test_data/` directory

This structure ensures clean separation of concerns, security best practices, and easy maintenance.
