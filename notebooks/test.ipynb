from dhanhq import dhanhq
import pandas as pd
import asyncio
import websockets
import json
from datetime import datetime
import csv



import pandas as pd
import json
import csv
from datetime import datetime
import nest_asyncio
import asyncio
import websockets
from dhanhq import dhanhq

# Allow asyncio inside Ju<PERSON>ter
nest_asyncio.apply()

# Your Dhan credentials (replace these with your actual values)
ACCESS_TOKEN = "your_access_token"
CLIENT_ID = "your_client_id"

# Example: Nifty 50 index instrument token
INSTRUMENT_TOKEN = "1330"  # Replace with your actual token

# Initialize Dhan client
dhan = dhanhq(client_id=CLIENT_ID, access_token=ACCESS_TOKEN)

# Store LTP values for EMA calculations
price_data = []

# CSV log file
CSV_FILE = "ema_crossover_log.csv"

# Initialize CSV log file
with open(CSV_FILE, mode="w", newline="") as f:
    writer = csv.writer(f)
    writer.writerow(["Timestamp", "Event", "Last Price", "EMA5", "EMA10"])

# Calculate EMA
def calculate_ema(prices, period):
    return pd.Series(prices).ewm(span=period, adjust=False).mean().iloc[-1]

# Detect crossover
def detect_crossover(prices):
    if len(prices) < 11:
        return None

    ema_5 = calculate_ema(prices, 5)
    ema_10 = calculate_ema(prices, 10)

    prev_ema_5 = calculate_ema(prices[:-1], 5)
    prev_ema_10 = calculate_ema(prices[:-1], 10)

    crossover = None
    if prev_ema_5 < prev_ema_10 and ema_5 > ema_10:
        crossover = "Bullish Crossover (5EMA > 10EMA)"
    elif prev_ema_5 > prev_ema_10 and ema_5 < ema_10:
        crossover = "Bearish Crossover (5EMA < 10EMA)"

    return crossover, ema_5, ema_10

# Async WebSocket stream
async def stream_data():
    url = f"wss://data.dhan.co/streaming/ws?client_id={CLIENT_ID}&token={ACCESS_TOKEN}"

    async with websockets.connect(url) as websocket:
        # Subscribe to LTP
        payload = {
            "msg_type": "subscribe",
            "exchange_segment": "NSE_EQ",  # or "NSE_IDX" if it's an index
            "instrument_id": INSTRUMENT_TOKEN,
            "subscription_mode": "LTP"
        }
        await websocket.send(json.dumps(payload))

        print("✅ Subscribed to live LTP stream...")

        async for message in websocket:
            data = json.loads(message)
            if 'ltp' in data:
                try:
                    ltp = float(data['ltp'])
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    print(f"[{timestamp}] LTP: {ltp}")

                    price_data.append(ltp)

                    if len(price_data) >= 11:
                        result = detect_crossover(price_data)
                        if result and result[0]:
                            event, ema5, ema10 = result
                            print(f"🔔 {event} | EMA5: {ema5:.2f} | EMA10: {ema10:.2f}")
                            with open(CSV_FILE, mode="a", newline="") as f:
                                writer = csv.writer(f)
                                writer.writerow([timestamp, event, ltp, round(ema5, 2), round(ema10, 2)])
                except Exception as e:
                    print(f"Error: {e}")

# Run the WebSocket streaming inside Jupyter
await stream_data()


