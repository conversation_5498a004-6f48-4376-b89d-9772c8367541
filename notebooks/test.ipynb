from dhanhq import dhanhq
import pandas as pd
import asyncio
import websockets
import json
from datetime import datetime
import csv

# Replace with your Dhan credentials
ACCESS_TOKEN = "1105577608"
CLIENT_ID = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# NIFTY 50 index instrument token (use correct token from Dhan)
INSTRUMENT_TOKEN = "1330"  # Example: Nifty 50 index

# Initialize Dhan client
dhan = dhanhq(client_id=CLIENT_ID, access_token=ACCESS_TOKEN)

# Stores historical prices for EMA calculation
price_data = []

# CSV log file
CSV_FILE = "ema_crossover_log.csv"

# Initialize CSV
with open(CSV_FILE, mode="w", newline="") as f:
    writer = csv.writer(f)
    writer.writerow(["Timestamp", "Event", "Last Price", "EMA5", "EMA10"])

# Calculate EMA
def calculate_ema(prices, period):
    return pd.Series(prices).ewm(span=period, adjust=False).mean().iloc[-1]

# Detect crossover
def detect_crossover(prices):
    if len(prices) < 11:
        return None  # Not enough data

    ema_5 = calculate_ema(prices, 5)
    ema_10 = calculate_ema(prices, 10)

    # Detect crossover logic
    prev_ema_5 = calculate_ema(prices[:-1], 5)
    prev_ema_10 = calculate_ema(prices[:-1], 10)

    crossover = None
    if prev_ema_5 < prev_ema_10 and ema_5 > ema_10:
        crossover = "Bullish Crossover (5EMA crossed above 10EMA)"
    elif prev_ema_5 > prev_ema_10 and ema_5 < ema_10:
        crossover = "Bearish Crossover (5EMA crossed below 10EMA)"

    return crossover, ema_5, ema_10

# WebSocket handler
async def stream_data():
    url = f"wss://data.dhan.co/streaming/ws?client_id={CLIENT_ID}&token={ACCESS_TOKEN}"

    async with websockets.connect(url) as websocket:
        # Subscribe to instrument
        payload = {
            "msg_type": "subscribe",
            "exchange_segment": "NSE_EQ",
            "instrument_id": INSTRUMENT_TOKEN,
            "subscription_mode": "LTP"
        }
        await websocket.send(json.dumps(payload))

        print("Subscribed to live LTP stream...")

        async for message in websocket:
            data = json.loads(message)

            if 'ltp' in data:
                ltp = float(data['ltp'])
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"[{timestamp}] LTP: {ltp}")

                price_data.append(ltp)

                if len(price_data) >= 11:
                    result = detect_crossover(price_data)
                    if result and result[0]:
                        event, ema5, ema10 = result
                        print(f"🔔 {event} | EMA5: {ema5:.2f} | EMA10: {ema10:.2f}")
                        with open(CSV_FILE, mode="a", newline="") as f:
                            writer = csv.writer(f)
                            writer.writerow([timestamp, event, ltp, round(ema5, 2), round(ema10, 2)])

# Run
asyncio.run(stream_data())
