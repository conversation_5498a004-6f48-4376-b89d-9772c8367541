{"cells": [{"cell_type": "code", "execution_count": 2, "id": "0addc01e", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'pandas_ta'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON><PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 22\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>q\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON><PERSON>\u001b[39;00m dhanhq\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mp<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpanda<PERSON>_ta\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mta\u001b[39;00m\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m     24\u001b[39m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcsv\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'pandas_ta'"]}], "source": ["# Replace with your <PERSON><PERSON> credentials\n", "ACCESS_TOKEN = \"1105577608\"\n", "CLIENT_ID = \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw\"\n", "\n", "from dhanhq import dhanhq\n", "import pandas as pd\n", "import asyncio\n", "import websockets\n", "import json\n", "from datetime import datetime\n", "import csv\n", "import pandas as pd\n", "import json\n", "import csv\n", "from datetime import datetime\n", "import nest_asyncio\n", "import asyncio\n", "import websockets\n", "from dhanhq import dhanhq\n", "\n", "import pandas as pd\n", "import pandas_ta as ta\n", "import json\n", "import csv\n", "from datetime import datetime\n", "import nest_asyncio\n", "import asyncio\n", "import websockets\n", "\n", "# Enable asyncio loop in Jupyter\n", "nest_asyncio.apply()\n", "\n", "\n", "\n", "# NSE instrument ID (e.g., NIFTY 50)\n", "INSTRUMENT_TOKEN = \"1330\"  # change this as needed\n", "\n", "# Log file\n", "CSV_FILE = \"ema_crossovers.csv\"\n", "\n", "# Initialize CSV with headers\n", "with open(CSV_FILE, mode=\"w\", newline=\"\") as f:\n", "    writer = csv.writer(f)\n", "    writer.writerow([\"Timestamp\", \"Event\", \"LTP\", \"EMA5\", \"EMA10\"])\n", "\n", "# LTP price list\n", "price_data = []\n", "\n", "# Detect EMA crossover using pandas_ta\n", "def detect_ema_crossover(prices):\n", "    df = pd.DataFrame(prices, columns=[\"close\"])\n", "    df[\"ema5\"] = ta.ema(df[\"close\"], length=5)\n", "    df[\"ema10\"] = ta.ema(df[\"close\"], length=10)\n", "\n", "    if df.shape[0] < 11:\n", "        return None, None, None\n", "\n", "    prev = df.iloc[-2]\n", "    curr = df.iloc[-1]\n", "\n", "    if pd.notna(prev[\"ema5\"]) and pd.notna(prev[\"ema10\"]):\n", "        if prev[\"ema5\"] < prev[\"ema10\"] and curr[\"ema5\"] > curr[\"ema10\"]:\n", "            return \"Bullish Crossover\", curr[\"ema5\"], curr[\"ema10\"]\n", "        elif prev[\"ema5\"] > prev[\"ema10\"] and curr[\"ema5\"] < curr[\"ema10\"]:\n", "            return \"Bearish Crossover\", curr[\"ema5\"], curr[\"ema10\"]\n", "    return None, curr[\"ema5\"], curr[\"ema10\"]\n", "\n", "# Async WebSocket streaming\n", "async def stream_data():\n", "    url = \"wss://streamapi.dhan.co/streaming/ws\"\n", "\n", "    async with websockets.connect(url) as websocket:\n", "        # Authenticate\n", "        await websocket.send(json.dumps({\n", "            \"msg_type\": \"auth\",\n", "            \"token\": ACCESS_TOKEN,\n", "            \"client_id\": CLIENT_ID\n", "        }))\n", "\n", "        # Subscribe to LTP\n", "        await websocket.send(json.dumps({\n", "            \"msg_type\": \"subscribe\",\n", "            \"exchange_segment\": \"NSE_IDX\",\n", "            \"instrument_id\": INSTRUMENT_TOKEN,\n", "            \"subscription_mode\": \"LTP\"\n", "        }))\n", "\n", "        print(\"✅ Subscribed to live LTP\")\n", "\n", "        async for message in websocket:\n", "            try:\n", "                data = json.loads(message)\n", "                if \"ltp\" in data:\n", "                    ltp = float(data[\"ltp\"])\n", "                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "                    print(f\"[{timestamp}] LTP: {ltp}\")\n", "                    price_data.append([ltp])\n", "\n", "                    if len(price_data) >= 11:\n", "                        event, ema5, ema10 = detect_ema_crossover(price_data)\n", "                        if event:\n", "                            print(f\"🔔 {event} | EMA5: {ema5:.2f} | EMA10: {ema10:.2f}\")\n", "                            with open(CSV_FILE, mode=\"a\", newline=\"\") as f:\n", "                                writer = csv.writer(f)\n", "                                writer.writerow([timestamp, event, ltp, round(ema5, 2), round(ema10, 2)])\n", "            except Exception as e:\n", "                print(f\"❌ Error: {e}\")\n", "\n", "# Run this in Jupyter\n", "await stream_data()\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "59e0ab4b", "metadata": {}, "outputs": [], "source": ["!uv pip install pandas_ta --quiet\n"]}], "metadata": {"kernelspec": {"display_name": "stock", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}