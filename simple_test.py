#!/usr/bin/env python3
"""
Simple test of enhanced EMA system
"""

import sys
import os
sys.path.append('src')

print("Starting simple test...")

try:
    print("Testing EMA Calculator...")
    from core.ema import EMACalculator
    
    ema_combinations = [{"short_ema": 5, "long_ema": 10}]
    calculator = EMACalculator(ema_combinations)
    
    # Test with some prices
    test_prices = [24500, 24505, 24510, 24508, 24512]
    for price in test_prices:
        emas = calculator.add_price("1min", price)
        print(f"Price {price}: EMAs = {emas}")
    
    print("✅ EMA Calculator test passed")
    
except Exception as e:
    print(f"❌ EMA Calculator test failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("\nTesting Signal Logger...")
    from data.logger import SignalLogger
    
    os.makedirs("test_data", exist_ok=True)
    logger = SignalLogger("test_data")
    
    # Test logging a signal
    from datetime import datetime
    test_signal = {
        'datetime': datetime.now(),
        'action': 'BUY',
        'price': 24750.50,
        'short_ema_value': 24748.25,
        'long_ema_value': 24745.80,
        'pnl': 0.0
    }
    
    logger.log_signal(test_signal)
    stats = logger.get_statistics()
    print(f"Logger stats: {stats}")
    logger.close()
    
    print("✅ Signal Logger test passed")
    
except Exception as e:
    print(f"❌ Signal Logger test failed: {e}")
    import traceback
    traceback.print_exc()

print("\nSimple test completed!")
