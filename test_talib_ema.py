#!/usr/bin/env python3
"""
Test TA-Lib Enhanced EMA System
===============================

This script tests the TA-Lib integration for fastest and most robust EMA calculations.
"""

import sys
import os
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append('src')

print("=" * 60)
print("TESTING TA-LIB ENHANCED EMA SYSTEM")
print("=" * 60)

def test_talib_availability():
    """Test TA-Lib availability"""
    print("\n🔍 Testing TA-Lib Availability")
    print("-" * 40)
    
    try:
        import numpy as np
        print("✅ NumPy available")
        NUMPY_AVAILABLE = True
    except ImportError:
        print("❌ NumPy not available")
        NUMPY_AVAILABLE = False
        return False
    
    try:
        import talib
        print("✅ TA-Lib available")
        print(f"   TA-Lib version: {talib.__version__ if hasattr(talib, '__version__') else 'Unknown'}")
        
        # Test basic EMA function
        test_data = np.array([100.0, 101.0, 102.0, 101.5, 103.0, 102.5, 104.0], dtype=np.float64)
        ema_result = talib.EMA(test_data, timeperiod=5)
        print(f"   Test EMA calculation: {ema_result[-1]:.4f}")
        
        return True
        
    except ImportError:
        print("❌ TA-Lib not available")
        return False
    except Exception as e:
        print(f"❌ TA-Lib test failed: {e}")
        return False


def test_enhanced_ema_calculator():
    """Test the enhanced EMA calculator with TA-Lib"""
    print("\n🧮 Testing Enhanced EMA Calculator with TA-Lib")
    print("-" * 50)
    
    try:
        from core.ema import EMACalculator
        
        # Configuration
        ema_combinations = [
            {"short_ema": 5, "long_ema": 10},
            {"short_ema": 8, "long_ema": 21}
        ]
        
        # Create enhanced EMA calculator
        calculator = EMACalculator(ema_combinations, max_history=2880)
        print("✅ EMA Calculator initialized")
        
        # Generate realistic test price data
        print("\nGenerating test price data...")
        base_price = 24500.0
        test_prices = []
        timestamps = []
        
        # Generate 50 data points with realistic price movement
        for i in range(50):
            # Simulate realistic price movement
            trend = i * 0.2  # Slight upward trend
            volatility = (i % 7 - 3) * 3.0  # Some volatility
            noise = (i % 3 - 1) * 1.5  # Random noise
            
            price = base_price + trend + volatility + noise
            test_prices.append(price)
            
            timestamp = datetime.now() - timedelta(minutes=50-i)
            timestamps.append(timestamp)
        
        print(f"Generated {len(test_prices)} test prices")
        print(f"Price range: {min(test_prices):.2f} - {max(test_prices):.2f}")
        
        # Test bulk loading with TA-Lib
        print("\nTesting bulk loading with TA-Lib...")
        calculator.load_state_from_prices("1min", test_prices, timestamps)
        
        # Get current EMA values
        current_emas = calculator.get_current_ema_values("1min")
        print(f"Current EMAs after bulk loading: {current_emas}")
        
        # Test real-time updates
        print("\nTesting real-time EMA updates...")
        for i in range(5):
            new_price = test_prices[-1] + (i - 2) * 5.0  # Some price movement
            new_timestamp = timestamps[-1] + timedelta(minutes=i+1)
            
            emas = calculator.add_price("1min", new_price, new_timestamp)
            print(f"Price {new_price:.2f}: EMAs = {emas}")
            
            # Check for crossover signals
            signals = calculator.get_crossover_signals("1min")
            if signals:
                for signal in signals:
                    print(f"  🔔 {signal['signal']} Signal: {signal['short_ema']}/{signal['long_ema']} "
                          f"(Short: {signal['short_value']:.2f}, Long: {signal['long_value']:.2f})")
        
        # Get statistics
        stats = calculator.get_statistics("1min")
        print(f"\nEMA Calculator Statistics:")
        print(f"  Timeframe: {stats['timeframe']}")
        print(f"  Price count: {stats['price_count']}")
        print(f"  Latest price: {stats['latest_price']:.2f}")
        print(f"  EMA status: {stats['ema_status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced EMA Calculator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_csv_integration():
    """Test CSV integration with enhanced system"""
    print("\n📄 Testing CSV Integration")
    print("-" * 40)
    
    try:
        from data.logger import SignalLogger
        
        # Create test data directory
        os.makedirs("test_data", exist_ok=True)
        
        # Create signal logger
        signal_logger = SignalLogger("test_data", initial_capital=100000)
        
        # Test signal logging with OHLC data
        test_signals = [
            {
                'datetime': datetime.now(),
                'action': 'BUY',
                'price': 24750.50,
                'ohlc': {
                    'open': 24745.00,
                    'high': 24755.00,
                    'low': 24740.00,
                    'close': 24750.50,
                    'volume': 1500
                },
                'short_ema_value': 24748.25,
                'long_ema_value': 24745.80,
                'pnl': 0.0
            },
            {
                'datetime': datetime.now() + timedelta(minutes=5),
                'action': 'SELL',
                'price': 24735.75,
                'ohlc': {
                    'open': 24750.00,
                    'high': 24752.00,
                    'low': 24735.00,
                    'close': 24735.75,
                    'volume': 1200
                },
                'short_ema_value': 24740.60,
                'long_ema_value': 24743.45,
                'pnl': -14.75
            }
        ]
        
        print(f"Logging {len(test_signals)} test signals...")
        for signal in test_signals:
            signal_logger.log_signal(signal)
        
        # Get statistics
        stats = signal_logger.get_statistics()
        print(f"Logger statistics: {stats}")
        
        # Check CSV file
        csv_file = stats.get('csv_file')
        if csv_file and os.path.exists(csv_file):
            with open(csv_file, 'r') as f:
                lines = f.readlines()
            print(f"CSV file: {csv_file}")
            print(f"Total lines: {len(lines)} (including header)")
            
            if len(lines) > 1:
                print("Recent signals:")
                for line in lines[-3:]:
                    if line.strip() and not line.startswith('Date'):
                        fields = line.strip().split(',')
                        if len(fields) >= 4:
                            print(f"  {fields[1]} - {fields[2]} @ {fields[3]}")
        
        signal_logger.close()
        return True
        
    except Exception as e:
        print(f"❌ CSV integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        # Test 1: TA-Lib availability
        talib_available = test_talib_availability()
        
        # Test 2: Enhanced EMA Calculator
        ema_success = test_enhanced_ema_calculator()
        
        # Test 3: CSV Integration
        csv_success = test_csv_integration()
        
        print("\n" + "=" * 60)
        if talib_available and ema_success and csv_success:
            print("🎉 ALL TA-LIB TESTS PASSED!")
            print("\nKey Features Tested:")
            print("  ✅ TA-Lib availability and basic functionality")
            print("  ✅ Enhanced EMA calculations with TA-Lib")
            print("  ✅ Bulk historical data loading with TA-Lib")
            print("  ✅ Real-time EMA updates")
            print("  ✅ Crossover signal detection")
            print("  ✅ CSV logging with OHLC data")
            print("  ✅ Enhanced data management")
        else:
            print("⚠️  SOME TESTS FAILED")
            if not talib_available:
                print("- TA-Lib not available or failed")
            if not ema_success:
                print("- Enhanced EMA calculator test failed")
            if not csv_success:
                print("- CSV integration test failed")
        
        print("\nNext Steps:")
        if not talib_available:
            print("1. Install TA-Lib: uv pip install --system TA-Lib")
            print("   (Note: May require system dependencies)")
        print("2. Check test_data/ directory for generated files")
        print("3. Review log output for detailed information")
        print("4. Integrate with live system for production testing")
        
    except Exception as e:
        print(f"❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
