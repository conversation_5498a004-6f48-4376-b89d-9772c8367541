# System Architecture

## Overview

The NIFTY 50 EMA Crossover Trading System is designed as a modular, scalable, and maintainable algorithmic trading platform. The architecture follows professional software development practices with clear separation of concerns.

## Core Components

### 1. Core Trading Engine (`src/core/`)

#### EMA Calculator (`ema.py`)
- **Purpose**: Exponential Moving Average calculations
- **Features**: 
  - Multiple EMA periods support
  - Crossover detection
  - State persistence
  - Historical data initialization

#### Strategy Engine (`strategy.py`)
- **Purpose**: Trading strategy implementation
- **Features**:
  - Tick data processing
  - Candle generation
  - Signal generation
  - P&L tracking

#### Market Feed (`market_feed.py`)
- **Purpose**: Real-time data acquisition
- **Features**:
  - DhanHQ WebSocket integration
  - Mock data generation
  - Automatic reconnection
  - Error handling

### 2. Data Management (`src/data/`)

#### Historical Database (`historical_database.py`)
- **Purpose**: 2-week rolling historical data management
- **Features**:
  - Dual storage (.pkl + .csv)
  - Daily updates
  - Automatic cleanup
  - API integration

#### Signal Logger (`logger.py`)
- **Purpose**: Signal logging and P&L tracking
- **Features**:
  - Daily CSV files
  - State continuity
  - Performance metrics
  - Report generation

#### Data Recovery (`historical_data.py`)
- **Purpose**: Historical signal reconstruction
- **Features**:
  - Intraday recovery
  - EMA state reconstruction
  - Gap filling

### 3. Utilities (`src/utils/`)

#### Market Hours Manager (`market_hours.py`)
- **Purpose**: Market timing and session management
- **Features**:
  - IST timezone handling
  - Trading day detection
  - Session state management
  - Holiday awareness

#### State Manager (`state_manager.py`)
- **Purpose**: System state persistence
- **Features**:
  - EMA state storage
  - Session continuity
  - Crash recovery
  - Daily reset

## Data Flow

```
Market Data → Market Feed → Strategy Engine → EMA Calculator
                                ↓
Signal Logger ← Signal Generation ← Crossover Detection
                                ↓
Historical Database ← State Manager ← System State
```

## Storage Architecture

### Historical Data
- **Location**: `data/historical/`
- **Format**: Binary (.pkl) + Human-readable (.csv)
- **Retention**: 14 trading days
- **Update**: Daily before market open

### Signal Data
- **Location**: `data/`
- **Format**: Daily CSV files
- **Naming**: `nifty50_ema_signals_YYYYMMDD.csv`
- **Content**: Complete signal history with P&L

### State Data
- **Location**: `data/`
- **Format**: Binary (.pkl)
- **Types**: EMA state, session state
- **Persistence**: Across system restarts

## Scalability Considerations

### Performance
- **Memory Usage**: ~50MB typical
- **CPU Usage**: Low (< 5% on modern systems)
- **Latency**: < 100ms signal detection
- **Throughput**: 1000+ ticks/second

### Extensibility
- **New Strategies**: Modular strategy interface
- **New Instruments**: Configurable instrument support
- **New Timeframes**: Flexible timeframe system
- **New Indicators**: Pluggable indicator architecture

## Security Architecture

### Data Protection
- **Credentials**: Secure storage, never logged
- **API Keys**: Environment variable support
- **File Permissions**: Restricted access
- **Network**: Secure WebSocket connections

### Error Handling
- **Graceful Degradation**: System continues on non-critical errors
- **Automatic Recovery**: Reconnection and state recovery
- **Comprehensive Logging**: Full audit trail
- **Monitoring**: Health checks and alerts

## Deployment Architecture

### Development
- **Local Testing**: Mock data support
- **Unit Tests**: Comprehensive test suite
- **Integration Tests**: End-to-end testing
- **Performance Tests**: Load testing

### Production
- **Daemon Mode**: Background operation
- **Process Management**: PID files and monitoring
- **Log Rotation**: Automatic log management
- **Health Monitoring**: Status checks and alerts

## Configuration Management

### Hierarchical Configuration
1. **Default Values**: Built-in defaults
2. **Config File**: `config/config.json`
3. **Environment Variables**: Runtime overrides
4. **Command Line**: Immediate overrides

### Environment-Specific
- **Development**: Mock data, verbose logging
- **Testing**: Isolated data, test credentials
- **Production**: Live data, optimized logging

## Monitoring and Observability

### Metrics
- **System Metrics**: CPU, memory, disk usage
- **Trading Metrics**: Signals, P&L, latency
- **Data Metrics**: Tick rates, connection status
- **Error Metrics**: Error rates, recovery times

### Logging
- **Structured Logging**: JSON format for parsing
- **Log Levels**: DEBUG, INFO, WARNING, ERROR
- **Log Rotation**: Daily rotation with retention
- **Centralized Logging**: Support for log aggregation

### Alerting
- **System Alerts**: Process down, high error rates
- **Trading Alerts**: Signal generation, P&L thresholds
- **Data Alerts**: Connection loss, data gaps
- **Performance Alerts**: High latency, memory usage

## Future Enhancements

### Planned Features
- **Multi-Instrument Support**: Beyond NIFTY 50
- **Advanced Strategies**: ML-based signals
- **Risk Management**: Position sizing, stop-loss
- **Portfolio Management**: Multi-strategy support

### Technical Improvements
- **Database Integration**: PostgreSQL/MongoDB support
- **Message Queues**: Redis/RabbitMQ integration
- **Microservices**: Service decomposition
- **Cloud Deployment**: Docker/Kubernetes support
