#!/usr/bin/env python3
"""
Minimal test to isolate import issues
"""

import sys
sys.path.append('src')

print("Testing minimal imports...")

try:
    print("1. Testing basic imports...")
    from collections import defaultdict, deque
    from typing import Dict, List, Optional
    from datetime import datetime, timedelta
    print("✅ Basic imports successful")
    
    print("2. Testing pandas imports...")
    try:
        import pandas as pd
        import numpy as np
        print("✅ pandas and numpy available")
        PANDAS_AVAILABLE = True
    except ImportError:
        print("⚠️ pandas/numpy not available")
        PANDAS_AVAILABLE = False
    
    print("3. Testing pandas-ta imports...")
    try:
        if PANDAS_AVAILABLE:
            import pandas_ta as ta
            print("✅ pandas-ta available")
        else:
            print("⚠️ pandas-ta skipped (pandas not available)")
    except Exception as e:
        print(f"⚠️ pandas-ta not available: {e}")
    
    print("4. Testing EMA Calculator class definition...")
    
    # Define a minimal EMA calculator without pandas-ta
    class MinimalEMACalculator:
        def __init__(self, ema_combinations):
            self.ema_combinations = ema_combinations
            self.ema_periods = set()
            for combo in ema_combinations:
                self.ema_periods.add(combo['short_ema'])
                self.ema_periods.add(combo['long_ema'])
            
            self.ema_values = defaultdict(lambda: defaultdict(lambda: deque(maxlen=1000)))
            self.price_history = defaultdict(lambda: deque(maxlen=1000))
            self.raw_prices = defaultdict(lambda: deque(maxlen=1000))
            self.multipliers = {period: 2.0 / (period + 1) for period in self.ema_periods}
            self.initialized = defaultdict(lambda: defaultdict(bool))
            
        def add_price(self, timeframe, price, timestamp=None):
            if timestamp is None:
                timestamp = datetime.now()
            
            self.price_history[timeframe].append((timestamp, price))
            self.raw_prices[timeframe].append(price)
            
            emas = {}
            for period in self.ema_periods:
                ema = self._calculate_ema_fallback(timeframe, period, price)
                if ema is not None:
                    emas[period] = ema
            return emas
        
        def _calculate_ema_fallback(self, timeframe, period, price):
            ema_history = self.ema_values[timeframe][period]
            
            if not self.initialized[timeframe][period]:
                raw_prices = list(self.raw_prices[timeframe])
                if len(raw_prices) >= period:
                    sma = sum(raw_prices[-period:]) / period
                    ema_history.append(sma)
                    self.initialized[timeframe][period] = True
                    return sma
                else:
                    return None
            else:
                if len(ema_history) > 0:
                    previous_ema = ema_history[-1]
                    multiplier = self.multipliers[period]
                    ema = (price * multiplier) + (previous_ema * (1 - multiplier))
                    ema_history.append(ema)
                    return ema
                else:
                    return None
    
    print("✅ Minimal EMA Calculator class defined")
    
    print("5. Testing minimal EMA calculator...")
    ema_combinations = [{"short_ema": 5, "long_ema": 10}]
    calculator = MinimalEMACalculator(ema_combinations)
    
    test_prices = [24500, 24505, 24510, 24508, 24512, 24515, 24520, 24518, 24525, 24530]
    for i, price in enumerate(test_prices):
        emas = calculator.add_price("1min", price)
        print(f"  Price {price}: EMAs = {emas}")
        
        if i >= 9:  # After enough data points
            # Simple crossover check
            short_history = calculator.ema_values["1min"][5]
            long_history = calculator.ema_values["1min"][10]
            
            if len(short_history) >= 2 and len(long_history) >= 2:
                short_current = short_history[-1]
                long_current = long_history[-1]
                short_previous = short_history[-2]
                long_previous = long_history[-2]
                
                if short_previous <= long_previous and short_current > long_current:
                    print(f"  🔔 BUY Signal detected!")
                elif short_previous >= long_previous and short_current < long_current:
                    print(f"  🔔 SELL Signal detected!")
    
    print("✅ Minimal EMA Calculator test successful")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()

print("\nMinimal test completed!")
