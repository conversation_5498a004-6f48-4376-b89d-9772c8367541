#!/usr/bin/env python3
"""
Test Script for EMA Crossover System
====================================

This script tests the EMA crossover system using mock data
to verify all components work correctly without requiring
a live DhanHQ connection.

Usage:
    python test_system.py

Author: AI Assistant
Date: 2025
"""

import sys
import os
import time
import json
from datetime import datetime

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger
from market_feed import MockMarketFeed


def test_ema_calculator():
    """Test EMA calculator functionality"""
    print("🧪 Testing EMA Calculator...")
    
    ema_combinations = [
        {"short_ema": 5, "long_ema": 10},
        {"short_ema": 8, "long_ema": 21}
    ]
    
    calculator = EMACalculator(ema_combinations)
    
    # Test with sample prices
    test_prices = [19500, 19505, 19510, 19508, 19512, 19515, 19520, 19518, 19525, 19530]
    
    for i, price in enumerate(test_prices):
        emas = calculator.add_price("1min", price)
        print(f"  Price {price}: EMAs = {emas}")
        
        if i >= 10:  # After enough data points
            signals = calculator.get_crossover_signals("1min")
            if signals:
                print(f"  🔔 Signals: {signals}")
    
    print("✅ EMA Calculator test completed\n")


def test_signal_logger():
    """Test signal logger functionality"""
    print("🧪 Testing Signal Logger...")
    
    # Create test logger
    logger = SignalLogger("test_data", ["1min"], 100000)
    
    # Test signal data
    test_signal = {
        'datetime': datetime.now(),
        'timeframe': '1min',
        'action': 'BUY',
        'price': 19500.50,
        'ema_combo': '5/10',
        'short_ema_value': 19498.25,
        'long_ema_value': 19495.80,
        'entry_exit': 'ENTRY',
        'pnl': 0.00,
        'candle_data': {
            'open': 19499.00,
            'high': 19501.25,
            'low': 19498.75,
            'close': 19500.50,
            'volume': 1250
        }
    }
    
    # Log test signal
    logger.log_signal(test_signal)
    
    # Get statistics
    stats = logger.get_statistics()
    print(f"  Logger stats: {stats}")
    
    # Close logger
    logger.close()
    
    print("✅ Signal Logger test completed\n")


def test_mock_system():
    """Test complete system with mock data"""
    print("🧪 Testing Complete System with Mock Data...")
    
    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    # Create components
    ema_calculator = EMACalculator(config['ema_combinations'])
    signal_logger = SignalLogger("test_data", config['timeframes'], 100000)
    
    strategy = EMAStrategy(
        ema_combinations=config['ema_combinations'],
        timeframes=config['timeframes'],
        ema_calculator=ema_calculator,
        signal_logger=signal_logger
    )
    
    # Create mock market feed
    mock_feed = MockMarketFeed(config['instrument'], strategy)
    
    # Start mock feed
    print("  Starting mock market feed for 30 seconds...")
    mock_feed.start()
    
    # Let it run for 30 seconds
    time.sleep(30)
    
    # Stop mock feed
    mock_feed.stop()
    
    # Get statistics
    strategy_stats = strategy.get_statistics()
    logger_stats = signal_logger.get_statistics()
    feed_stats = mock_feed.get_statistics()
    
    print(f"  Strategy stats: {strategy_stats}")
    print(f"  Logger stats: {logger_stats}")
    print(f"  Feed stats: {feed_stats}")
    
    # Close logger
    signal_logger.close()
    
    print("✅ Complete system test completed\n")


def main():
    """Run all tests"""
    print("=" * 60)
    print("EMA CROSSOVER SYSTEM - TEST SUITE")
    print("=" * 60)
    print()
    
    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        # Run tests
        test_ema_calculator()
        test_signal_logger()
        test_mock_system()
        
        print("🎉 All tests completed successfully!")
        print("\nNext steps:")
        print("1. Update your DhanHQ credentials in config/config.json")
        print("2. Run: python src/main.py")
        print("3. Check data/ directory for CSV output files")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
