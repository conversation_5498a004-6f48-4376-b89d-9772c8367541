#!/usr/bin/env python3
"""
Test Historical Database
========================

Test the 2-week historical database functionality to verify it can
fetch, store, and manage historical NIFTY 50 data properly.
"""

import sys
import os
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path
sys.path.append('src')

from historical_database import HistoricalDatabase
from ema import EMACalculator


def test_database_creation():
    """Test historical database creation and basic functionality"""
    print("🧪 Testing Historical Database Creation...")

    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)

    # Create historical database (credentials loaded from env vars)
    db = HistoricalDatabase(
        market_hours_config=config.get('market_hours', {}),
        data_directory="test_data"
    )

    # Test trading days calculation
    trading_days = db.get_trading_days_range()
    print(f"  Trading days range: {len(trading_days)} days")
    print(f"  From: {trading_days[0]} to {trading_days[-1]}")

    # Test database info
    db_info = db.get_database_info()
    print(f"  Database status: {db_info['status']}")
    print(f"  Total days: {db_info['total_days']}")
    print(f"  Total candles: {db_info['total_candles']}")

    # Test update need check
    update_needed = db.is_update_needed()
    print(f"  Update needed: {update_needed}")

    return True


def test_data_update():
    """Test historical data update functionality"""
    print("\n🧪 Testing Historical Data Update...")

    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)

    # Create historical database (credentials loaded from env vars)
    db = HistoricalDatabase(
        market_hours_config=config.get('market_hours', {}),
        data_directory="test_data"
    )

    print("  Starting historical data update (this may take a few minutes)...")

    # Update historical data
    success = db.update_historical_data(
        security_id=config['instrument']['security_id'],
        exchange_segment=config['instrument']['exchange_segment']
    )

    print(f"  Update success: {success}")

    if success:
        # Get updated database info
        db_info = db.get_database_info()
        print(f"  Updated database:")
        print(f"    Total days: {db_info['total_days']}")
        print(f"    Total candles: {db_info['total_candles']}")
        print(f"    Date range: {db_info['date_range']['start']} to {db_info['date_range']['end']}")
        print(f"    Database size: {db_info['size_mb']:.2f} MB")
        print(f"    Last updated: {db_info['last_updated']}")

        return True

    return False


def test_ema_initialization():
    """Test EMA initialization with historical data"""
    print("\n🧪 Testing EMA Initialization with Historical Data...")

    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)

    # Create historical database (credentials loaded from env vars)
    db = HistoricalDatabase(
        market_hours_config=config.get('market_hours', {}),
        data_directory="test_data"
    )

    # Get historical prices
    historical_prices = db.get_historical_prices(days=5)  # Last 5 days
    print(f"  Retrieved {len(historical_prices)} historical prices")

    if historical_prices:
        print(f"  Price range: {min(historical_prices):.2f} to {max(historical_prices):.2f}")
        print(f"  First price: {historical_prices[0]:.2f}")
        print(f"  Last price: {historical_prices[-1]:.2f}")

        # Test EMA initialization
        ema_calculator = EMACalculator(config['ema_combinations'])

        print("  Initializing EMAs with historical data...")
        ema_calculator.load_state_from_prices("1min", historical_prices)

        # Get current EMA values
        current_emas = ema_calculator.get_current_ema_values("1min")

        if current_emas:
            print(f"  EMA initialization successful:")
            for ema_key, value in current_emas.items():
                print(f"    {ema_key}: {value:.2f}")

            return True
        else:
            print("  ❌ EMA initialization failed")
            return False
    else:
        print("  ❌ No historical prices available")
        return False


def test_directory_structure():
    """Test directory structure and file organization"""
    print("\n🧪 Testing Directory Structure...")

    base_dir = "test_data"
    historical_dir = os.path.join(base_dir, "historical")

    print(f"  Base directory: {base_dir}")
    print(f"  Historical directory: {historical_dir}")

    # Check if directories exist
    if os.path.exists(historical_dir):
        print("  ✅ Historical directory exists")

        # List files in historical directory
        files = os.listdir(historical_dir)
        print(f"  Files in historical directory: {len(files)}")

        for file in files:
            file_path = os.path.join(historical_dir, file)
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"    {file}: {size_mb:.2f} MB")

        return True
    else:
        print("  ❌ Historical directory does not exist")
        return False


def test_data_retrieval():
    """Test data retrieval and filtering"""
    print("\n🧪 Testing Data Retrieval...")

    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)

    # Create historical database (credentials loaded from env vars)
    db = HistoricalDatabase(
        market_hours_config=config.get('market_hours', {}),
        data_directory="test_data"
    )

    # Test different retrieval options
    test_cases = [
        ("All available data", None),
        ("Last 3 days", 3),
        ("Last 7 days", 7),
        ("Last 1 day", 1)
    ]

    for description, days in test_cases:
        prices = db.get_historical_prices(days=days)
        print(f"  {description}: {len(prices)} prices")

        if prices:
            # Calculate some statistics
            price_change = prices[-1] - prices[0]
            price_change_pct = (price_change / prices[0]) * 100
            print(f"    Price change: {price_change:+.2f} ({price_change_pct:+.2f}%)")

    return True


def main():
    """Run historical database tests"""
    print("=" * 60)
    print("TESTING 2-WEEK HISTORICAL DATABASE")
    print("=" * 60)

    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)

        # Run tests
        tests = [
            ("Database Creation", test_database_creation),
            ("Data Update", test_data_update),
            ("EMA Initialization", test_ema_initialization),
            ("Directory Structure", test_directory_structure),
            ("Data Retrieval", test_data_retrieval)
        ]

        passed = 0
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                if test_func():
                    print(f"✅ {test_name} PASSED")
                    passed += 1
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} ERROR: {e}")

        print("\n" + "=" * 60)
        print(f"TEST RESULTS: {passed}/{len(tests)} PASSED")
        print("=" * 60)

        if passed == len(tests):
            print("🎉 ALL HISTORICAL DATABASE TESTS PASSED!")
            print("\nThe enhanced system now provides:")
            print("  ✅ 2-week rolling historical database")
            print("  ✅ Daily automatic data updates")
            print("  ✅ Separate historical data storage")
            print("  ✅ EMA initialization with historical data")
            print("  ✅ Efficient data management and cleanup")

            print("\n🚀 Ready for production with historical database!")
            print("  python ema_daemon.py restart")

        else:
            print("⚠️  Some tests failed. Check the output above.")

        print("\nNext steps:")
        print("1. Stop current system: python ema_daemon.py stop")
        print("2. Restart with historical database: python ema_daemon.py start")
        print("3. Monitor logs for historical data updates")
        print("4. Check data/historical/ directory for database files")

    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
