#!/usr/bin/env python3
"""
Test State Persistence
======================

Test the enhanced EMA system's ability to maintain state across restarts
within the same trading day.
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger
from state_manager import StateManager


def test_state_persistence():
    """Test EMA state persistence across system restarts"""
    print("🧪 Testing EMA State Persistence...")
    
    # Configuration
    ema_combinations = [{'short_ema': 5, 'long_ema': 10}]
    timeframes = ['1min']
    
    # Create components for first session
    print("\n📊 Session 1: Initial data processing...")
    
    state_manager1 = StateManager("test_data")
    ema_calculator1 = EMACalculator(ema_combinations)
    logger1 = SignalLogger("test_data", 100000)
    
    strategy1 = EMAStrategy(
        ema_combinations=ema_combinations,
        timeframes=timeframes,
        ema_calculator=ema_calculator1,
        signal_logger=logger1,
        state_manager=state_manager1
    )
    
    # Generate first batch of data
    base_time = datetime.now()
    first_batch_prices = [19500, 19495, 19490, 19485, 19480, 19475, 19470, 19465, 19460, 19455]
    
    print(f"  Processing {len(first_batch_prices)} prices in first session...")
    
    for i, price in enumerate(first_batch_prices):
        tick_data = {
            'timestamp': base_time + timedelta(minutes=i),
            'price': price,
            'volume': 100,
            'security_id': '13',
            'exchange_segment': 'IDX_I'
        }
        strategy1.process_tick(tick_data)
    
    # Force completion and save state
    strategy1.force_candle_completion()
    state_manager1.save_daily_state()
    
    # Get first session results
    stats1 = strategy1.get_statistics()
    logger_stats1 = logger1.get_statistics()
    ema_values1 = ema_calculator1.get_current_ema_values('1min')
    
    print(f"  Session 1 Results:")
    print(f"    Candles: {sum(stats1['candles_generated'].values())}")
    print(f"    Signals: {logger_stats1['total_signals']}")
    print(f"    EMA Values: {ema_values1}")
    
    # Close first session
    logger1.close()
    
    # Simulate system restart - create new components
    print("\n🔄 System Restart: Loading previous state...")
    
    state_manager2 = StateManager("test_data")
    ema_calculator2 = EMACalculator(ema_combinations)
    logger2 = SignalLogger("test_data", 100000)
    
    strategy2 = EMAStrategy(
        ema_combinations=ema_combinations,
        timeframes=timeframes,
        ema_calculator=ema_calculator2,
        signal_logger=logger2,
        state_manager=state_manager2
    )
    
    # Load historical state
    strategy2.load_historical_state()
    
    # Get EMA values after loading
    ema_values2 = ema_calculator2.get_current_ema_values('1min')
    logger_stats2 = logger2.get_statistics()
    
    print(f"  After loading state:")
    print(f"    EMA Values: {ema_values2}")
    print(f"    Existing signals: {logger_stats2['total_signals']}")
    print(f"    Cumulative P&L: {logger_stats2['cumulative_pnl']:.2f}")
    
    # Process second batch of data
    print("\n📊 Session 2: Continuing with new data...")
    
    second_batch_prices = [19460, 19465, 19470, 19475, 19480, 19485, 19490, 19495, 19500, 19505]
    
    print(f"  Processing {len(second_batch_prices)} additional prices...")
    
    for i, price in enumerate(second_batch_prices):
        tick_data = {
            'timestamp': base_time + timedelta(minutes=len(first_batch_prices) + i),
            'price': price,
            'volume': 100,
            'security_id': '13',
            'exchange_segment': 'IDX_I'
        }
        strategy2.process_tick(tick_data)
    
    # Force completion
    strategy2.force_candle_completion()
    
    # Get final results
    stats2 = strategy2.get_statistics()
    logger_stats2_final = logger2.get_statistics()
    ema_values2_final = ema_calculator2.get_current_ema_values('1min')
    
    print(f"  Session 2 Final Results:")
    print(f"    Total Candles: {sum(stats2['candles_generated'].values())}")
    print(f"    Total Signals: {logger_stats2_final['total_signals']}")
    print(f"    Final EMA Values: {ema_values2_final}")
    print(f"    Final P&L: {logger_stats2_final['cumulative_pnl']:.2f}")
    
    # Close second session
    logger2.close()
    
    # Verify state persistence
    print("\n✅ Verification:")
    
    success = True
    
    # Check EMA continuity
    if ema_values1 and ema_values2:
        ema5_diff = abs(ema_values1.get('EMA5', 0) - ema_values2.get('EMA5', 0))
        ema10_diff = abs(ema_values1.get('EMA10', 0) - ema_values2.get('EMA10', 0))
        
        if ema5_diff < 0.01 and ema10_diff < 0.01:
            print("  ✅ EMA values preserved correctly across restart")
        else:
            print(f"  ❌ EMA values differ: EMA5 diff={ema5_diff:.4f}, EMA10 diff={ema10_diff:.4f}")
            success = False
    else:
        print("  ❌ Missing EMA values")
        success = False
    
    # Check signal continuity
    expected_total_candles = len(first_batch_prices) + len(second_batch_prices)
    actual_total_candles = sum(stats2['candles_generated'].values())
    
    if actual_total_candles >= expected_total_candles * 0.9:  # Allow some tolerance
        print(f"  ✅ Candle processing continued correctly: {actual_total_candles} candles")
    else:
        print(f"  ❌ Candle count mismatch: expected ~{expected_total_candles}, got {actual_total_candles}")
        success = False
    
    # Check CSV continuity
    if logger_stats2_final['total_signals'] >= logger_stats1['total_signals']:
        print(f"  ✅ Signal logging continued: {logger_stats2_final['total_signals']} total signals")
    else:
        print(f"  ❌ Signal count decreased: {logger_stats1['total_signals']} -> {logger_stats2_final['total_signals']}")
        success = False
    
    return success


def test_daily_csv_continuity():
    """Test that CSV file maintains continuity across restarts"""
    print("\n🧪 Testing Daily CSV Continuity...")
    
    # Check if today's CSV file exists and has data
    today = datetime.now().strftime("%Y%m%d")
    csv_file = f"test_data/nifty50_ema_signals_{today}.csv"
    
    if os.path.exists(csv_file):
        with open(csv_file, 'r') as f:
            lines = f.readlines()
            
        print(f"  CSV file exists: {csv_file}")
        print(f"  Total lines: {len(lines)} (including header)")
        
        if len(lines) > 1:
            print("  ✅ CSV contains signal data")
            
            # Show last few signals
            print("  Recent signals:")
            for line in lines[-3:]:
                if line.strip() and not line.startswith('Date'):
                    fields = line.strip().split(',')
                    if len(fields) >= 4:
                        print(f"    {fields[1]} - {fields[2]} @ {fields[3]}")
            
            return True
        else:
            print("  ⚠️  CSV file exists but has no data")
            return False
    else:
        print(f"  ⚠️  No CSV file found for today: {csv_file}")
        return False


def main():
    """Run state persistence tests"""
    print("=" * 60)
    print("TESTING EMA STATE PERSISTENCE")
    print("=" * 60)
    
    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)
        
        # Run tests
        test1_success = test_state_persistence()
        test2_success = test_daily_csv_continuity()
        
        print("\n" + "=" * 60)
        if test1_success and test2_success:
            print("🎉 ALL STATE PERSISTENCE TESTS PASSED!")
            print("\nThe enhanced system now provides:")
            print("  ✅ EMA state persistence across restarts")
            print("  ✅ Continuous signal generation within trading day")
            print("  ✅ Daily CSV file continuity")
            print("  ✅ P&L tracking across sessions")
            print("\n🚀 System is ready for production with full state management!")
        else:
            print("⚠️  Some state persistence tests failed")
            if not test1_success:
                print("  - EMA state persistence issues")
            if not test2_success:
                print("  - CSV continuity issues")
        
        print("\nUsage for production:")
        print("  python src/main.py --background  # Runs with state persistence")
        print("  python ema_daemon.py start       # Daemon mode with auto-restart")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
