#!/usr/bin/env python3
"""
EMA Daemon
==========

Daemon management script for the Enhanced EMA Trading System.
Provides start, stop, status, and log management functionality.
"""

import sys
import os
import time
import signal
import argparse
import subprocess
from datetime import datetime

class EMADaemon:
    def __init__(self):
        self.pid_file = "ema_system.pid"
        self.log_file = "logs/ema_daemon.log"
        self.main_script = "src/main.py"
        
        # Ensure logs directory exists
        os.makedirs("logs", exist_ok=True)
    
    def start(self, config_file=None, background=True):
        """Start the EMA system as a daemon"""
        if self.is_running():
            print("❌ EMA system is already running")
            return False
        
        print("🚀 Starting EMA Trading System...")
        
        # Build command
        cmd = [sys.executable, self.main_script]
        if config_file:
            cmd.extend(["--config", config_file])
        if background:
            cmd.append("--background")
        
        try:
            if background:
                # Start as background process
                process = subprocess.Popen(
                    cmd,
                    stdout=open(self.log_file, 'a'),
                    stderr=subprocess.STDOUT,
                    preexec_fn=os.setsid
                )
                
                # Save PID
                with open(self.pid_file, 'w') as f:
                    f.write(str(process.pid))
                
                # Wait a moment to check if it started successfully
                time.sleep(2)
                if process.poll() is None:
                    print(f"✅ EMA system started successfully (PID: {process.pid})")
                    print(f"📊 Monitor logs: tail -f {self.log_file}")
                    return True
                else:
                    print("❌ EMA system failed to start")
                    return False
            else:
                # Start in foreground
                subprocess.run(cmd)
                return True
                
        except Exception as e:
            print(f"❌ Failed to start EMA system: {e}")
            return False
    
    def stop(self):
        """Stop the EMA system daemon"""
        if not self.is_running():
            print("❌ EMA system is not running")
            return False
        
        try:
            pid = self.get_pid()
            print(f"🛑 Stopping EMA system (PID: {pid})...")
            
            # Send SIGTERM
            os.kill(pid, signal.SIGTERM)
            
            # Wait for graceful shutdown
            for _ in range(10):
                if not self.is_running():
                    break
                time.sleep(1)
            
            if self.is_running():
                print("⚠️  Graceful shutdown failed, forcing stop...")
                os.kill(pid, signal.SIGKILL)
                time.sleep(1)
            
            # Clean up PID file
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
            
            print("✅ EMA system stopped successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to stop EMA system: {e}")
            return False
    
    def restart(self, config_file=None):
        """Restart the EMA system daemon"""
        print("🔄 Restarting EMA system...")
        self.stop()
        time.sleep(2)
        return self.start(config_file)
    
    def status(self):
        """Check the status of the EMA system"""
        print("📊 EMA TRADING SYSTEM STATUS")
        print("=" * 40)
        
        if self.is_running():
            pid = self.get_pid()
            print(f"✅ Status: Running (PID: {pid})")
            
            # Get process info
            try:
                import psutil
                process = psutil.Process(pid)
                print(f"📈 CPU Usage: {process.cpu_percent():.1f}%")
                print(f"💾 Memory Usage: {process.memory_info().rss / 1024 / 1024:.1f} MB")
                print(f"⏰ Started: {datetime.fromtimestamp(process.create_time()).strftime('%Y-%m-%d %H:%M:%S')}")
            except ImportError:
                print("📊 Process info: Install psutil for detailed stats")
            except Exception as e:
                print(f"📊 Process info: {e}")
        else:
            print("❌ Status: Not running")
        
        # Check log file
        if os.path.exists(self.log_file):
            stat = os.stat(self.log_file)
            print(f"📄 Log file: {self.log_file}")
            print(f"📏 Log size: {stat.st_size / 1024:.1f} KB")
            print(f"🕐 Last modified: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check today's signals
        today = datetime.now().strftime('%Y%m%d')
        signal_file = f"data/signals/nifty50_ema_signals_{today}.csv"
        if os.path.exists(signal_file):
            try:
                with open(signal_file, 'r') as f:
                    lines = f.readlines()
                    signal_count = len(lines) - 1  # Exclude header
                print(f"🔔 Today's signals: {signal_count}")
            except:
                print("🔔 Today's signals: Unable to read")
        else:
            print("🔔 Today's signals: No file found")
    
    def logs(self, follow=False, tail=None, date=None):
        """View system logs"""
        if date:
            log_file = f"logs/ema_system_{date.replace('-', '')}.log"
        else:
            log_file = self.log_file
        
        if not os.path.exists(log_file):
            print(f"❌ Log file not found: {log_file}")
            return
        
        try:
            if follow:
                subprocess.run(["tail", "-f", log_file])
            elif tail:
                subprocess.run(["tail", f"-{tail}", log_file])
            else:
                subprocess.run(["cat", log_file])
        except KeyboardInterrupt:
            pass
        except Exception as e:
            print(f"❌ Error viewing logs: {e}")
    
    def is_running(self):
        """Check if the daemon is running"""
        if not os.path.exists(self.pid_file):
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Check if process exists
            os.kill(pid, 0)
            return True
        except (OSError, ValueError):
            # Process doesn't exist, clean up stale PID file
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
            return False
    
    def get_pid(self):
        """Get the PID of the running daemon"""
        if os.path.exists(self.pid_file):
            with open(self.pid_file, 'r') as f:
                return int(f.read().strip())
        return None

def main():
    """Main daemon management function"""
    parser = argparse.ArgumentParser(description="EMA Trading System Daemon Management")
    parser.add_argument("command", choices=["start", "stop", "restart", "status", "logs"],
                       help="Daemon command")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--follow", action="store_true", help="Follow logs in real-time")
    parser.add_argument("--tail", type=int, help="Show last N lines of logs")
    parser.add_argument("--date", help="View logs for specific date (YYYY-MM-DD)")
    parser.add_argument("--foreground", action="store_true", help="Start in foreground mode")
    
    args = parser.parse_args()
    
    daemon = EMADaemon()
    
    if args.command == "start":
        daemon.start(args.config, not args.foreground)
    elif args.command == "stop":
        daemon.stop()
    elif args.command == "restart":
        daemon.restart(args.config)
    elif args.command == "status":
        daemon.status()
    elif args.command == "logs":
        daemon.logs(args.follow, args.tail, args.date)

if __name__ == "__main__":
    main()
