#!/usr/bin/env python3
"""
EMA Trading System Daemon
=========================

This script provides daemon-like functionality for the EMA trading system.
It can start, stop, restart, and check the status of the background process.

Usage:
    python ema_daemon.py start    # Start in background
    python ema_daemon.py stop     # Stop background process
    python ema_daemon.py restart  # Restart background process
    python ema_daemon.py status   # Check status
    python ema_daemon.py logs     # Show recent logs

Author: AI Assistant
Date: 2025
"""

import os
import sys
import time
import signal
import subprocess
from pathlib import Path


class EMADaemon:
    """Daemon manager for EMA trading system"""
    
    def __init__(self):
        self.pid_file = Path("ema_system.pid")
        self.log_file = Path("logs/ema_daemon.log")
        self.script_path = Path("src/main.py")
        
        # Ensure logs directory exists
        self.log_file.parent.mkdir(exist_ok=True)
    
    def start(self):
        """Start the EMA system in background"""
        if self.is_running():
            print("❌ EMA system is already running")
            return False
        
        print("🚀 Starting EMA Trading System in background...")
        
        try:
            # Start the process in background
            with open(self.log_file, 'a') as log:
                process = subprocess.Popen([
                    sys.executable, str(self.script_path), '--background'
                ], stdout=log, stderr=log, preexec_fn=os.setsid)
            
            # Save PID
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            # Wait a moment to check if process started successfully
            time.sleep(2)
            
            if self.is_running():
                print(f"✅ EMA system started successfully (PID: {process.pid})")
                print(f"📝 Logs: {self.log_file}")
                print("💡 Use 'python ema_daemon.py logs' to monitor")
                return True
            else:
                print("❌ Failed to start EMA system")
                return False
                
        except Exception as e:
            print(f"❌ Error starting EMA system: {e}")
            return False
    
    def stop(self):
        """Stop the EMA system"""
        if not self.is_running():
            print("❌ EMA system is not running")
            return False
        
        try:
            pid = self.get_pid()
            if pid:
                print(f"🛑 Stopping EMA system (PID: {pid})...")
                
                # Send SIGTERM for graceful shutdown
                os.killpg(os.getpgid(pid), signal.SIGTERM)
                
                # Wait for process to stop
                for i in range(10):
                    if not self.is_running():
                        break
                    time.sleep(1)
                
                # Force kill if still running
                if self.is_running():
                    print("⚠️  Process didn't stop gracefully, force killing...")
                    os.killpg(os.getpgid(pid), signal.SIGKILL)
                    time.sleep(1)
                
                # Clean up PID file
                if self.pid_file.exists():
                    self.pid_file.unlink()
                
                if not self.is_running():
                    print("✅ EMA system stopped successfully")
                    return True
                else:
                    print("❌ Failed to stop EMA system")
                    return False
                    
        except Exception as e:
            print(f"❌ Error stopping EMA system: {e}")
            return False
    
    def restart(self):
        """Restart the EMA system"""
        print("🔄 Restarting EMA Trading System...")
        self.stop()
        time.sleep(2)
        return self.start()
    
    def status(self):
        """Check status of EMA system"""
        if self.is_running():
            pid = self.get_pid()
            uptime = self.get_uptime()
            print(f"🟢 EMA system is RUNNING (PID: {pid})")
            print(f"⏱️  Uptime: {uptime}")
            
            # Show recent log entries
            if self.log_file.exists():
                print("\n📝 Recent log entries:")
                try:
                    with open(self.log_file, 'r') as f:
                        lines = f.readlines()
                        for line in lines[-5:]:  # Last 5 lines
                            print(f"   {line.strip()}")
                except Exception:
                    pass
        else:
            print("🔴 EMA system is NOT running")
            
            # Check if PID file exists but process is dead
            if self.pid_file.exists():
                print("⚠️  Stale PID file found, cleaning up...")
                self.pid_file.unlink()
    
    def logs(self, follow=False, lines=50):
        """Show logs"""
        if not self.log_file.exists():
            print("❌ No log file found")
            return
        
        try:
            if follow:
                # Follow logs (like tail -f)
                print(f"📝 Following logs from {self.log_file} (Ctrl+C to stop)...")
                subprocess.run(['tail', '-f', str(self.log_file)])
            else:
                # Show last N lines
                print(f"📝 Last {lines} lines from {self.log_file}:")
                print("-" * 60)
                subprocess.run(['tail', '-n', str(lines), str(self.log_file)])
                
        except KeyboardInterrupt:
            print("\n👋 Stopped following logs")
        except Exception as e:
            print(f"❌ Error reading logs: {e}")
    
    def is_running(self):
        """Check if EMA system is running"""
        pid = self.get_pid()
        if not pid:
            return False
        
        try:
            # Check if process exists
            os.kill(pid, 0)
            return True
        except OSError:
            return False
    
    def get_pid(self):
        """Get PID from PID file"""
        if not self.pid_file.exists():
            return None
        
        try:
            with open(self.pid_file, 'r') as f:
                return int(f.read().strip())
        except (ValueError, IOError):
            return None
    
    def get_uptime(self):
        """Get process uptime"""
        pid = self.get_pid()
        if not pid:
            return "Unknown"
        
        try:
            # Get process start time
            stat_file = f"/proc/{pid}/stat"
            if os.path.exists(stat_file):
                with open(stat_file, 'r') as f:
                    fields = f.read().split()
                    start_time = int(fields[21])  # Process start time in clock ticks
                
                # Calculate uptime
                with open('/proc/uptime', 'r') as f:
                    system_uptime = float(f.read().split()[0])
                
                clock_ticks_per_second = os.sysconf(os.sysconf_names['SC_CLK_TCK'])
                process_start_seconds = start_time / clock_ticks_per_second
                process_uptime = system_uptime - process_start_seconds
                
                # Format uptime
                hours = int(process_uptime // 3600)
                minutes = int((process_uptime % 3600) // 60)
                seconds = int(process_uptime % 60)
                
                return f"{hours}h {minutes}m {seconds}s"
            else:
                return "Unknown"
                
        except Exception:
            return "Unknown"


def main():
    """Main entry point"""
    daemon = EMADaemon()
    
    if len(sys.argv) < 2:
        print("Usage: python ema_daemon.py {start|stop|restart|status|logs}")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'start':
        daemon.start()
    elif command == 'stop':
        daemon.stop()
    elif command == 'restart':
        daemon.restart()
    elif command == 'status':
        daemon.status()
    elif command == 'logs':
        follow = '--follow' in sys.argv or '-f' in sys.argv
        daemon.logs(follow=follow)
    else:
        print(f"❌ Unknown command: {command}")
        print("Available commands: start, stop, restart, status, logs")
        sys.exit(1)


if __name__ == "__main__":
    main()
